import type { PlayPetParams, PlayPetResponse } from "@/features/pets/types/pets";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UsePlayPetOptions {
    onSuccess?: (data: PlayPetResponse) => void;
    onError?: (error: Error) => void;
}

const usePlayPet = (options: UsePlayPetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.play.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Play pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default usePlayPet;
