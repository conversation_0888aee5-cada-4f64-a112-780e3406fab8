import { describe, expect, it } from "vitest";
import { getObjectiveProgress } from "../getObjectiveProgress";
import { QuestObjectiveTypes } from "@/types/quest";

describe("getObjectiveProgress for GATHER_RESOURCES", () => {
    it("should return progress count from quest_objective_progress", () => {
        const objective = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 5,
            target: 200,
            targetAction: "mining",
            itemId: 200,
            quest_objective_progress: [
                { count: 2 },
                { count: 1 },
            ],
        } as any;

        const inventory = [] as any;
        const result = getObjectiveProgress(objective, inventory);
        expect(result).toBe(3);
    });

    it("should return 0 when no progress exists", () => {
        const objective = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 5,
            target: 200,
            targetAction: "mining",
            itemId: 200,
            quest_objective_progress: [],
        } as any;

        const inventory = [] as any;
        const result = getObjectiveProgress(objective, inventory);
        expect(result).toBe(0);
    });

    it("should handle single progress entry", () => {
        const objective = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 3,
            target: null,
            targetAction: "scavenging",
            itemId: null,
            quest_objective_progress: [
                { count: 2 },
            ],
        } as any;

        const inventory = [] as any;
        const result = getObjectiveProgress(objective, inventory);
        expect(result).toBe(2);
    });

    it("should work for any activity type objectives", () => {
        const objective = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 10,
            target: 201,
            targetAction: null,
            itemId: 201,
            quest_objective_progress: [
                { count: 3 },
                { count: 2 },
                { count: 1 },
            ],
        } as any;

        const inventory = [] as any;
        const result = getObjectiveProgress(objective, inventory);
        expect(result).toBe(6);
    });
});
