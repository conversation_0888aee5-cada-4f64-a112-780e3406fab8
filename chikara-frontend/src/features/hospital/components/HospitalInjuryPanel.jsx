import yenImg from "@/assets/icons/UI/yen.png";
import Button from "@/components/Buttons/Button";
import { Checkbox } from "@/components/ui/checkbox";
import { statusEffects } from "@/helpers/statusEffects";
import useGameConfig from "@/hooks/useGameConfig";
import { cn } from "@/lib/utils";
import { useState } from "react";
import useHospitalCheckIn from "../api/useHospitalCheckIn";
import { useGetStatusEffects } from "@/hooks/api/useGetStatusEffects";

const HospitalInjuryPanel = ({ currentUser }) => {
    const { data: allEffects } = useGetStatusEffects();
    const { hospitalCheckIn } = useHospitalCheckIn();
    const { MINOR_COST_PER_LEVEL, MODERATE_COST_PER_LEVEL, SEVERE_COST_PER_LEVEL, COST_PER_HP } = useGameConfig();
    const [healInjuriesOnly, setHealInjuriesOnly] = useState(false);

    const missingHealth = currentUser?.health - currentUser?.currentHealth;

    const injuries = allEffects.filter((effect) => effect.effect && effect.effect.effectType === "DEBUFF");

    const calculateTotalHospitalCost = () => {
        if (!injuries || !currentUser) return 0;
        const hpToHeal = currentUser.health - currentUser.currentHealth;
        const userLevel = currentUser.level;

        const minorInjuries = injuries.filter((i) => i.effect.tier === "Minor");
        const moderateInjuries = injuries.filter((i) => i.effect.tier === "Moderate");
        const severeInjuries = injuries.filter((i) => i.effect.tier === "Severe");

        const minorCost = minorInjuries?.length * (userLevel * MINOR_COST_PER_LEVEL) || 0;
        const moderateCost = moderateInjuries?.length * (userLevel * MODERATE_COST_PER_LEVEL) || 0;
        const severeCost = severeInjuries?.length * (userLevel * SEVERE_COST_PER_LEVEL) || 0;
        const hpCost = hpToHeal * COST_PER_HP || 0;

        let total = minorCost + moderateCost + severeCost;

        if (!healInjuriesOnly) total += hpCost;
        return Math.round(total);
    };

    const calculateCost = (injuryType, count) => {
        if (!injuries || !currentUser) return 0;
        const userLevel = currentUser.level;
        let cost = 0;

        switch (injuryType) {
            case "Minor":
                cost = userLevel * MINOR_COST_PER_LEVEL;
                break;
            case "Moderate":
                cost = userLevel * MODERATE_COST_PER_LEVEL;
                break;
            case "Severe":
                cost = userLevel * SEVERE_COST_PER_LEVEL;
                break;
            case "missingHP":
                cost = count * COST_PER_HP;
                break;
            default:
                cost = 0;
                break;
        }
        return Math.round(cost);
    };

    const getInjuryColor = (injuryType) => {
        switch (injuryType) {
            case "Minor":
                return "text-red-400";
            case "Moderate":
                return "text-red-500";
            case "Severe":
                return "text-red-600";
            case "missingHP":
                return "text-red-400";
            default:
                return "text-red-400";
        }
    };

    const totalCost = calculateTotalHospitalCost(currentUser);

    return (
        <div className="mx-auto my-4 flex w-[90%] flex-col justify-between rounded-lg border border-gray-600 bg-slate-800 px-4 py-2 md:w-fit md:min-w-96 md:px-6">
            <div className="flex flex-col">
                <div className="flex flex-row justify-between">
                    <p className="mb-0.5 text-blue-300 text-sm uppercase">Your Injuries</p>
                    {!currentUser?.hospitalisedUntil && (
                        <div className="flex items-center space-x-2 md:my-auto md:hidden dark:text-gray-200">
                            <Checkbox
                                disabled={!totalCost || injuries.length < 1}
                                checked={healInjuriesOnly}
                                onCheckedChange={(checked) => setHealInjuriesOnly(checked)}
                            />
                            <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                Heal Injuries Only
                            </label>
                        </div>
                    )}
                </div>

                {injuries?.length === 0 && missingHealth === 0 && <li className="text-gray-300 text-sm">None</li>}
                {injuries?.map((injury) => (
                    <div key={injury.id}>
                        {injury.effect.name !== "Zombified" && (
                            <li className="flex flex-row gap-1.5 text-gray-200">
                                {injury.stacks}x{" "}
                                <img
                                    src={statusEffects[injury?.effect?.source]?.icon}
                                    alt=""
                                    className="mt-0.5 h-5 w-auto rounded-md border border-red-500"
                                />
                                <p className={getInjuryColor(injury.effect.tier)}>{injury.effect.name}</p>
                                <div className="ml-2 flex flex-row items-center gap-1 text-yellow-500">
                                    <img className="mb-0.5 h-4 w-auto" src={yenImg} alt="" />
                                    <p className="text-yellow-500">{calculateCost(injury.effect.tier)}</p>
                                </div>
                            </li>
                        )}
                    </div>
                ))}

                {missingHealth > 0 && (
                    <>
                        <hr className="my-2 border-gray-600/75" />
                        <div>
                            <li
                                className={cn(
                                    healInjuriesOnly && "line-through",
                                    "flex flex-row gap-1.5 text-gray-200"
                                )}
                            >
                                {missingHealth}
                                <p className={getInjuryColor("missingHP")}>Health Points</p>
                                <div className="ml-2 flex flex-row items-center gap-1 text-yellow-500">
                                    <img className="mb-0.5 h-4 w-auto" src={yenImg} alt="" />
                                    <p className="text-yellow-500">{calculateCost("missingHP", missingHealth)}</p>
                                </div>
                            </li>
                        </div>
                    </>
                )}
            </div>
            <hr className="my-2 border-gray-600/75" />

            {currentUser?.hospitalisedUntil ? (
                <div className="flex flex-col items-center">
                    <p className="text-green-500 text-lg">Receiving Treatment</p>
                </div>
            ) : (
                <div className="flex flex-row items-end gap-6">
                    {totalCost > 0 ? (
                        <>
                            <div className="flex flex-col">
                                <p className="text-gray-300 text-xs uppercase">Length</p>
                                <div className="flex flex-row items-center gap-1.5 text-blue-500 text-lg">
                                    <p>10 Minutes</p>
                                </div>
                            </div>
                            <div className="ml-auto flex flex-col">
                                <p className="text-gray-300 text-xs uppercase">Total Cost</p>
                                <div className="flex flex-row items-center gap-1.5 text-xl text-yellow-500">
                                    <img className="h-6 w-auto" src={yenImg} alt="" />
                                    <p>{totalCost}</p>
                                </div>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="flex flex-col">
                                <p className="text-gray-300 text-xs uppercase">Length</p>
                                <div className="text-center text-xl">-</div>
                            </div>
                            <div className="ml-auto flex flex-col">
                                <p className="text-gray-300 text-xs uppercase">Total Cost</p>
                                <div className="text-center text-xl">-</div>
                            </div>
                        </>
                    )}

                    <div className="flex flex-col md:flex-row md:gap-6">
                        <div className="hidden items-center space-x-2 md:my-auto md:flex dark:text-gray-200">
                            <Checkbox
                                checked={healInjuriesOnly}
                                disabled={!totalCost || injuries.length < 1}
                                onCheckedChange={(checked) => setHealInjuriesOnly(checked)}
                            />
                            <label className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                                Heal Injuries Only
                            </label>
                        </div>

                        <Button
                            className="w-24! ml-auto!"
                            disabled={!totalCost}
                            onClick={() => hospitalCheckIn.mutate(healInjuriesOnly)}
                        >
                            Check In
                        </Button>
                    </div>
                </div>
            )}
        </div>
    );
};

export default HospitalInjuryPanel;
