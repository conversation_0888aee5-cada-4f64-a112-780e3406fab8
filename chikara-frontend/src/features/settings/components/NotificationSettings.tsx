import ToggleSwitch from "@/components/ToggleSwitch";
import useSetNotificationSettings from "../api/useSetNotificationSettings";
import { User } from "@/types/user";

export default function NotificationSettings({ currentUser }: { currentUser: User }) {
    const { pushNotificationsEnabled, setPushNotificationsEnabled, saveNotificationSettings } =
        useSetNotificationSettings(currentUser);

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        saveNotificationSettings();
    };

    return (
        <form className="divide-y divide-gray-200 lg:col-span-9 dark:divide-gray-600" onSubmit={handleSubmit}>
            <div className="px-4 py-2 sm:p-6 md:py-6 lg:pb-8">
                <div>
                    <h2 className="mt-3 font-medium text-gray-900 text-lg text-stroke-sm leading-6 dark:text-gray-200">
                        Notifications
                    </h2>
                </div>
                <ToggleSwitch
                    label="Push Notifications"
                    description="Receive push/web notifications"
                    value={pushNotificationsEnabled}
                    onChange={setPushNotificationsEnabled}
                />
                {/* {pushNotificationsEnabled && (
          <ul>
            <ToggleSwitch
              label="AP Full"
              value={pushNotificationsEnabled}
              onChange={setPushNotificationsEnabled}
            />
            <ToggleSwitch
              label="Health Full"
              value={pushNotificationsEnabled}
              onChange={setPushNotificationsEnabled}
            />
            <ToggleSwitch
              label="Energy Full"
              disabled={true}
              value={pushNotificationsEnabled}
              onChange={setPushNotificationsEnabled}
            />
          </ul>
        )} */}
            </div>

            <div className="mt-4 flex justify-end p-4 sm:px-6">
                <button
                    type="submit"
                    className="ml-5 inline-flex w-1/5 justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-stroke-sm text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-400 focus:ring-offset-2"
                >
                    Save
                </button>
            </div>
        </form>
    );
}
