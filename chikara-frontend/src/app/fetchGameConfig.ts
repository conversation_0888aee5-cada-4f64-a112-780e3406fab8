import { persistStore } from "@/app/store/persistStore";
import { client } from "@/lib/orpc";

export const fetchGameConfig = async (newVersion?: number) => {
    const { setGameConfig } = persistStore.getState();

    try {
        console.log("Updating game config to " + (newVersion ?? "latest"));
        const data = await client.user.getGameConfig();

        if (data) {
            setGameConfig(data);
            console.log(`GameConfig fetched: ${data.version}`);
        } else {
            console.error("Failed to fetch game config");
        }

        return { data, error: null };
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : "Unknown error";
        console.error("Failed to fetch game config:", errorMessage);
        return { data: null, error: errorMessage };
    }
};
