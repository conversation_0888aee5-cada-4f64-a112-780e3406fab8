import { UserItemModel, db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
import type { Prisma } from "@prisma/client";

/**
 * Find all items in the database
 */
export const findAllItems = async () => {
    return await db.item.findMany();
};

/**
 * Find all items with type "upgrade"
 */
export const findUpgradeItems = async () => {
    return await db.item.findMany({
        where: { itemType: "upgrade" },
    });
};

/**
 * Create a new item
 */
export const createItem = async (data: Prisma.itemCreateInput) => {
    return await db.item.create({ data });
};

/**
 * Find an item by its ID
 */
export const findItemById = async (id: number) => {
    return await db.item.findUnique({
        where: { id },
    });
};

/**
 * Update an item by ID
 */
export const updateItem = async (id: number, data: Prisma.itemUpdateInput) => {
    return await db.item.update({
        where: { id },
        data,
    });
};

/**
 * Find a user item with its associated item
 */
export const findUserItemWithItem = async (id: number) => {
    return await db.user_item.findUnique({
        where: { id },
        include: { item: true },
    });
};

/**
 * Find a user item by user item id and user id
 */
export const findUserItemById = async (userItemId: number, userId: number) => {
    return await db.user_item.findUnique({
        where: { id: userItemId, userId },
        include: { item: true },
    });
};

/**
 * Find item by name
 */
export const findItemByName = async (name: string) => {
    return await db.item.findFirst({
        where: { name },
        select: { id: true },
    });
};

/**
 * Create multiple private messages (for megaphone)
 */
export const createGlobalMessages = async (senderId: number, message: string, userIds: number[]) => {
    const createdAt = getNow();
    const updatedAt = getNow();

    return await db.private_message.createMany({
        data: userIds.map((userId) => ({
            message,
            senderId,
            receiverId: userId,
            read: false,
            isGlobal: true,
            createdAt,
            updatedAt,
        })),
    });
};

/**
 * Find all users (minimal fields)
 */
export const findAllUserIds = async () => {
    return await db.user.findMany({
        select: { id: true },
    });
};

/**
 * Update user health status
 */
export const updateUserHealth = async (userId: number, data: Prisma.userUpdateInput) => {
    return await db.user.update({
        where: { id: userId },
        data,
    });
};

/**
 * Find gang by ID
 */
export const findGangById = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
    });
};

/**
 * Update gang resources
 */
export const updateGangResources = async (gangId: number, materialsToAdd = 0, toolsToAdd = 0) => {
    const gang = await findGangById(gangId);
    if (!gang) return null;

    return await db.gang.update({
        where: { id: gangId },
        data: {
            materialsResource: (gang.materialsResource || 0) + materialsToAdd,
            toolsResource: (gang.toolsResource || 0) + toolsToAdd,
        },
    });
};

/**
 * Update gang member resources
 */
export const updateGangMemberResources = async (gangMemberId: number, materialsToAdd = 0, toolsToAdd = 0) => {
    const member = await db.gang_member.findUnique({
        where: { id: gangMemberId },
    });
    if (!member) return null;

    return await db.gang_member.update({
        where: { id: gangMemberId },
        data: {
            weeklyMaterials: (member.weeklyMaterials || 0) + materialsToAdd,
            weeklyTools: (member.weeklyTools || 0) + toolsToAdd,
        },
    });
};

/**
 * Find gang member
 */
export const findGangMember = async (userId: number) => {
    return await db.gang_member.findFirst({
        where: { userId: userId },
    });
};

/**
 * Find items by multiple IDs
 */
export const findItemsByIds = async (itemIds: number[]) => {
    return await db.item.findMany({
        where: {
            id: { in: itemIds },
        },
    });
};

/**
 * Find items by multiple IDs with specific fields for scavenging
 */
export const findItemsByIdsForScavenging = async (itemIds: number[]) => {
    return await db.item.findMany({
        where: {
            id: { in: itemIds },
        },
        select: {
            id: true,
            name: true,
            image: true,
            itemType: true,
            rarity: true,
            cashValue: true,
        },
    });
};

/**
 * Find items by IDs with specific fields for names
 */
export const findItemNamesById = async (itemIds: number[]): Promise<Record<number, string>> => {
    if (!itemIds || itemIds.length === 0) {
        return {};
    }
    const items = await db.item.findMany({
        where: { id: { in: itemIds } },
        select: { id: true, name: true },
    });

    return items.reduce(
        (acc, item) => {
            acc[item.id] = item.name;
            return acc;
        },
        {} as Record<number, string>
    );
};

/**
 * Find item with cash value constraint
 */
export const findItemWithCashValue = async (itemId: number) => {
    return await db.item.findUnique({
        where: {
            id: itemId,
            cashValue: { not: null },
        },
    });
};

/**
 * Bulk create items
 */
export const bulkCreateItems = async (items: Prisma.itemCreateInput[]) => {
    return await db.item.createMany({ data: items }).then(() => db.item.findMany());
};
