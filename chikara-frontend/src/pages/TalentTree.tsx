import DisplayTalent from "@/features/talents/components/DisplayTalent";
import TalentInfoModal from "@/features/talents/components/TalentInfoModal";
import { talentData } from "@/features/talents/data/talentData";
import type { TalentInfo, UnlockedTalents, StatTypes } from "@/features/talents/types/talents";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGetUserSkills from "@/hooks/api/useGetUserSkills";
import useGetUnlockedTalents from "@/features/talents/api/useGetUnlockedTalents";
import { cn } from "@/lib/utils";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useCallback, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import TalentNav from "./TalentNav";

type PathName = `/talents/${StatTypes}`;

const urls: Record<PathName, { long: StatTypes; short: string }> = {
    "/talents/strength": { long: "strength", short: "STR" },
    "/talents/dexterity": { long: "dexterity", short: "DEX" },
    "/talents/defence": { long: "defence", short: "DEF" },
    "/talents/intelligence": { long: "intelligence", short: "INT" },
    "/talents/endurance": { long: "endurance", short: "END" },
    "/talents/vitality": { long: "vitality", short: "VIT" },
};

// Talent tier breakpoints now align with skill levels 5, 10, 20, and 40.
// Tier 0 (first row) is unlocked by default (statReq: 0).
const tierDividers = [
    { tier: "0", statReq: 0, className: "", indexes: [0, 1, 2] },
    { tier: "1", statReq: 5, className: "topDivider", indexes: [3, 4, 5] },
    { tier: "2", statReq: 10, className: "secondDivider", indexes: [6, 7, 8] },
    { tier: "3", statReq: 20, className: "thirdDivider", indexes: [9, 10, 11] },
    {
        tier: "4",
        statReq: 40,
        className: "fourthDivider",
        indexes: [12, 13, 14],
    },
];

const showGridNumbers = false;
const showEmptySpaces = false;

interface TalentsProps {
    pathname: string;
}

export default function Talents({ pathname }: TalentsProps) {
    const [talentFilter, setTalentFilter] = useState<StatTypes | undefined>();
    const [selectedTalent, setSelectedTalent] = useState<TalentInfo | undefined>();
    const { data: currentUser } = useFetchCurrentUser();
    const { data: userSkills } = useGetUserSkills();
    const navigate = useNavigate();
    const currentStatLong = (urls as Record<string, { long: StatTypes; short: string }>)[pathname]?.long;
    const currentStatShort = (urls as Record<string, { long: StatTypes; short: string }>)[pathname]?.short;

    const currentTab = (tabname: string): boolean => {
        if (pathname === `/talents/${tabname}`) {
            return true;
        } else {
            return false;
        }
    };

    const tabs = [
        {
            name: "STRENGTH",
            leftLink: null,
            rightLink: "dexterity",
            current: currentTab("strength"),
        },
        {
            name: "DEXTERITY",
            leftLink: "strength",
            rightLink: "defence",
            current: currentTab("dexterity"),
        },
        {
            name: "DEFENCE",
            leftLink: "dexterity",
            rightLink: "intelligence",
            current: currentTab("defence"),
        },
        {
            name: "INTELLIGENCE",
            leftLink: "defence",
            rightLink: "endurance",
            current: currentTab("intelligence"),
        },
        {
            name: "ENDURANCE",
            leftLink: "intelligence",
            rightLink: "vitality",
            current: currentTab("endurance"),
        },
        {
            name: "VITALITY",
            leftLink: "endurance",
            rightLink: null,
            current: currentTab("vitality"),
        },
    ];

    useEffect(() => {
        if (pathname && !(urls as Record<string, { long: StatTypes; short: string }>)[pathname]) {
            navigate("/talents");
        }
        const currentLocation = currentStatLong;
        if (currentLocation === talentFilter) return;
        setTalentFilter(currentLocation);
    }, [pathname, currentStatLong, talentFilter, navigate]);

    const filterTalents = useCallback(
        (talents: TalentInfo[]) => {
            if (!talentFilter) return null;
            const filteredTalents = talents.filter((talent: TalentInfo) => talent.tree === talentFilter);
            // Add position data and level descriptions
            const updatedTalents = filteredTalents.map((talent: TalentInfo) => {
                const uiData = talentData[talent.name as keyof typeof talentData];

                return {
                    ...talent,
                    ...(uiData || {}),
                };
            });
            return updatedTalents;
        },
        [talentFilter]
    );

    const filterUnlockedTalents = useCallback(
        (talents: UnlockedTalents | undefined) => {
            if (!talentFilter || !talents) return null;
            const talentList = talents.talentList;
            const filteredTalents = talentList?.filter((talent) => talent.talentInfo.tree === talentFilter);
            return filteredTalents;
        },
        [talentFilter]
    );

    const { data } = useQuery(
        api.talents.getTalents.queryOptions({
            staleTime: Infinity,
            select: filterTalents,
        })
    );
    const { data: unlockedTalentsData } = useGetUnlockedTalents();
    const { data: unlockedTalents } = useGetUnlockedTalents({
        select: filterUnlockedTalents,
    });

    const gridArray: (TalentInfo | { talentType: string })[] = new Array(15).fill({
        talentType: "empty",
    });

    // Assign talents to grid positions
    data?.forEach((talent: TalentInfo) => {
        if (talent.position !== undefined) {
            const position = talent.position - 1;
            gridArray[position] = talent;
        }
    });

    const isTalentDisabled = (index: number): boolean => {
        for (const divider of tierDividers) {
            if (divider.indexes.includes(index)) {
                if (userSkills && currentStatLong) {
                    const currentSkillLevel = userSkills[currentStatLong]?.level || 1;
                    if (currentSkillLevel < divider.statReq) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            }
        }
        return false;
    };

    const isSelectedTalentDisabled = (): boolean => {
        if (!userSkills || !currentStatLong || !selectedTalent) return true;
        const currentSkillLevel = userSkills[currentStatLong]?.level || 1;
        if (currentSkillLevel < selectedTalent.skillLevelRequired) {
            return true;
        }
        return false;
    };
    return (
        <div className="mx-auto h-[calc(100dvh-9.5rem)] md:h-[75dvh] md:max-w-4xl md:rounded-xl md:border-2 md:border-gray-600 md:bg-gray-200 md:dark:bg-gray-800">
            <div className="h-full flex-1 flex-col">
                <TalentNav
                    tabs={tabs}
                    setSelectedTalent={setSelectedTalent}
                    talentPoints={currentUser?.talentPoints}
                    currentTreePoints={unlockedTalentsData?.treePoints?.[currentStatLong as StatTypes]}
                />
                <motion.span
                    key={talentFilter}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    <div className="relative grid h-[calc(100%-6rem)] w-full grid-cols-3 grid-rows-5 gap-4 md:h-[calc(100%-120px)]">
                        {tierDividers.map(({ tier, statReq, className }) => (
                            <div
                                key={tier}
                                className={cn(
                                    `absolute w-full ${className} flex shadow-2xl`,
                                    userSkills &&
                                        currentStatLong &&
                                        statReq <= (userSkills[currentStatLong]?.level || 1) &&
                                        "brightness-75",
                                    tier !== "0" && "h-1 bg-slate-700"
                                )}
                            >
                                {userSkills &&
                                currentStatLong &&
                                statReq > (userSkills[currentStatLong]?.level || 1) ? (
                                    <>
                                        {tier !== "0" && (
                                            <img
                                                className="-translate-x-1/2 -top-3 md:-top-5 absolute left-1/2 h-6 w-auto bg-[#f8f8fb] px-2 md:h-10 md:bg-gray-200 md:px-3 dark:bg-[#111521] md:dark:bg-gray-800"
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/static/talents/icons/lock.png`}
                                                alt=""
                                            />
                                        )}

                                        <p
                                            className={cn(
                                                tier !== "0" ? "mt-1" : "mt-0.5",
                                                "z-20 ml-2 text-gray-900 text-shadow-s-md text-sm dark:text-gray-300"
                                            )}
                                        >
                                            Requires {statReq} {currentStatShort}
                                        </p>
                                    </>
                                ) : null}
                            </div>
                        ))}
                        {gridArray.map((talent, i) => (
                            <div key={i} className="relative flex size-full">
                                <DisplayTalent
                                    talent={talent}
                                    selectedTalent={selectedTalent}
                                    setSelectedTalent={setSelectedTalent}
                                    isTalentDisabled={isTalentDisabled(i)}
                                    showEmptySpaces={showEmptySpaces}
                                    unlockedTalents={unlockedTalents}
                                />
                            </div>
                        ))}

                        {selectedTalent && (
                            <TalentInfoModal
                                selectedTalent={selectedTalent}
                                setSelectedTalent={setSelectedTalent}
                                treePoints={unlockedTalentsData?.treePoints?.[currentStatLong as StatTypes]}
                                unlockedTalents={unlockedTalents}
                                talentPoints={currentUser?.talentPoints}
                                isTalentDisabled={isSelectedTalentDisabled()}
                            />
                        )}
                    </div>
                </motion.span>
                {showGridNumbers && (
                    <div className="absolute top-20 left-0 grid h-[calc(100%-5rem)] w-full grid-cols-3 grid-rows-5 gap-4">
                        {gridArray.map((_, index) => (
                            <div key={index} className="flex">
                                <p className="m-auto rounded-full border-2 border-black bg-yellow-500 px-2 text-2xl text-stroke-s-md text-white drop-shadow-2xl dark:border-white">
                                    {index + 1}
                                </p>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    );
}
