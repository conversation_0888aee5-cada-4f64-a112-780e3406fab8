import gangCredImg from "@/assets/icons/UI/currency2.png";
import LoadingState from "@/components/LoadingState";
import { api } from "@/helpers/api";
import { UTCDateMini } from "@date-fns/utc";
import { useQuery } from "@tanstack/react-query";

const getPayoutTime = () => {
    // Returns time until the end of the week in days and hours
    const currentDate = new UTCDateMini();

    // Calculate the end of the week (Sunday at 23:59:59.999)
    const endOfWeek = new UTCDateMini(
        currentDate.getUTCFullYear(),
        currentDate.getUTCMonth(),
        currentDate.getUTCDate() + (7 - currentDate.getUTCDay())
    );
    endOfWeek.setUTCHours(23, 59, 59, 999);

    const timeDifference = endOfWeek.getTime() - currentDate.getTime();
    const daysUntilEndOfWeek = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
    const hoursUntilEndOfWeek = Math.floor((timeDifference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

    return `${daysUntilEndOfWeek} day${daysUntilEndOfWeek !== 1 ? "s" : ""} and ${hoursUntilEndOfWeek} hour${hoursUntilEndOfWeek !== 1 ? "s" : ""}`;
};

const GangPayoutsInfo = ({ currentGang, currentUserId }) => {
    const { data: memberShares, isLoading } = useQuery(
        api.gang.getMemberShares.queryOptions({
            input: { gangId: currentGang?.id },
            enabled: !!currentGang?.id,
            staleTime: 0.5 * 60 * 60 * 1000,
        })
    );

    const currentMember = memberShares?.memberShares?.find((el) => el.userId === currentUserId);

    const getWeeklyContributionTotal = (member) => {
        return member.weeklyMaterials + member.weeklyEssence * 3 + member.weeklyTools * 2;
    };

    const contributions = currentGang?.gang_member.reduce((acc, member) => {
        return acc + getWeeklyContributionTotal(member);
    }, 0);

    const respectBonus = memberShares?.gangRankingMultiplier;
    const earnedCreds = Math.round(contributions / 5);
    const totalEarnedCreds = Math.round(earnedCreds * respectBonus);
    const gangMemberTax = Math.round(earnedCreds * (currentGang?.gang_member.length * 0.02));
    const gangTotalPool = Math.round(Math.max(totalEarnedCreds - gangMemberTax, 0));

    const currentUserShare = Math.round(currentMember?.payoutShare);

    const getPredictedPayout = () => {
        return Math.round(gangTotalPool * (currentMember?.payoutShare / 100));
    };

    return (
        <div className="flex flex-col gap-2">
            <p className="mt-1 ml-9 text-left text-gray-200 text-sm uppercase leading-none">Payouts</p>

            <div className="mx-6 flex">
                <div className="mx-auto w-full rounded-lg border-2 border-indigo-600 bg-slate-800 py-3 text-center lg:px-14">
                    <p className="text-gray-300 text-xs uppercase">Time Until Payout</p>
                    <p className="mt-1 text-custom-yellow text-xl">{getPayoutTime()}</p>
                </div>
            </div>

            <div className="mx-auto w-full rounded-lg border-2 border-indigo-600 bg-slate-800 py-1.5 text-center">
                <LoadingState size={10} className="mx-auto" isLoading={isLoading}>
                    <div className="flex flex-col gap-1.5">
                        <div className="mx-auto grid grid-cols-2 items-center gap-2">
                            <p className="w-24 text-gray-300 text-xs uppercase">Earned Creds</p>
                            <p className="text-custom-yellow text-sm">{earnedCreds}</p>
                        </div>
                        <hr className="my-0.5 border-gray-600/25" />
                        <div className="mx-auto grid grid-cols-2 items-center gap-2">
                            <p className="w-24 text-gray-300 text-xs uppercase">Member Tax</p>
                            <p className="text-red-500 text-sm">-{gangMemberTax}</p>
                        </div>
                        <hr className="my-0.5 border-gray-600/25" />
                        <div className="mx-auto grid grid-cols-2 items-center gap-2">
                            <p className="w-24 text-gray-300 text-xs uppercase">Respect Bonus</p>
                            <p className="text-red-400 text-sm">{respectBonus > 1 ? `x${respectBonus}` : "-"}</p>
                        </div>

                        <hr className="my-0.5 border-gray-600/25" />
                        <div className="mx-auto grid grid-cols-2 items-center gap-2">
                            <p className="mr-2 text-gray-200 text-sm uppercase">Gang Total</p>
                            <div className="flex items-center justify-center gap-2">
                                <img className="aspect-square h-8 w-auto" src={gangCredImg} alt="Gang creds" />
                                <p className="mt-1 text-green-500 text-lg">{gangTotalPool}</p>
                            </div>
                        </div>
                    </div>
                    <hr className="my-1 border-gray-600/25" />
                    <div className="flex justify-center gap-5">
                        <div className="flex flex-col gap-0.5">
                            <p className="mt-3 text-gray-300 text-xs uppercase">Your Share</p>
                            <div className="flex items-center justify-center gap-2">
                                <p className="text-blue-500 text-lg">{currentUserShare}%</p>
                            </div>
                        </div>
                        <div className="flex flex-col gap-0.5">
                            <p className="mt-3 text-gray-300 text-xs uppercase">Your Predicted Payout</p>
                            <div className="flex items-center justify-center gap-2">
                                <img className="aspect-square h-8 w-auto" src={gangCredImg} alt="Gang creds" />
                                <p className="text-green-500 text-lg">{getPredictedPayout()}</p>
                            </div>
                        </div>
                    </div>
                </LoadingState>
            </div>
        </div>
    );
};

export default GangPayoutsInfo;
