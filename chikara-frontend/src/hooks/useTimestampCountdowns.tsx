import type { CraftingQueueItem } from "@/features/crafting/types/crafting";
import { api } from "@/helpers/api";
import { getMillisecondsUntilTimestamp, hasTimestampExpired } from "@/utils/timestampHelpers";
import type { QueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import toast from "react-hot-toast";
import { useNormalStore } from "../app/store/stores";
import type { User } from "../types/user";

const useTimestampCountdowns = (
    userData: User | null | undefined,
    craftingQueue: CraftingQueueItem[] | undefined,
    refetchUserData: () => void,
    queryClient: QueryClient
): void => {
    const { setJustJailed, setCraftCollectReady, craftCollectReady } = useNormalStore();

    useEffect(() => {
        if (userData?.courseIndex && userData?.courseEnds) {
            const courseUntil = new Date(userData.courseEnds);
            const now = Date.now();
            const millisecondsRemaining = courseUntil.getTime() - now;
            if (millisecondsRemaining > 0) {
                const timeout = setTimeout(() => {
                    toast.success("Your course was completed!");
                    refetchUserData();
                }, millisecondsRemaining);
                return () => clearTimeout(timeout);
            }
        }
    }, [userData?.courseIndex, userData?.courseEnds, refetchUserData]);

    useEffect(() => {
        if (userData?.hospitalisedUntil) {
            const hospitalisedUntil = new Date(userData.hospitalisedUntil);
            const now = Date.now();
            const millisecondsRemaining = hospitalisedUntil.getTime() - now;
            if (millisecondsRemaining > 0) {
                const timeout = setTimeout(() => {
                    toast.success("You were released from hospital!");
                    refetchUserData();
                    queryClient.invalidateQueries({
                        queryKey: api.user.getStatusEffects.key(),
                    });
                    queryClient.invalidateQueries({
                        queryKey: api.infirmary.getHospitalList.key(),
                    });
                }, millisecondsRemaining + 500);
                return () => clearTimeout(timeout);
            }
        }
    }, [userData?.hospitalisedUntil, refetchUserData, queryClient]);

    useEffect(() => {
        if (userData?.jailedUntil) {
            const jailedUntil = new Date(userData.jailedUntil);
            const now = Date.now();
            const millisecondsRemaining = jailedUntil.getTime() - now;
            if (millisecondsRemaining > 0) {
                const timeout = setTimeout(() => {
                    toast.success("You were released from jail!");
                    refetchUserData();
                    setJustJailed(false);
                }, millisecondsRemaining);
                return () => clearTimeout(timeout);
            } else {
                setJustJailed(false);
            }
        } else {
            setJustJailed(false);
        }
    }, [userData?.jailedUntil, refetchUserData, setJustJailed]);

    useEffect(() => {
        if (userData?.battleValidUntil) {
            const battleUntil = new Date(userData.battleValidUntil);
            const now = Date.now();
            const millisecondsRemaining = battleUntil.getTime() - now;
            if (millisecondsRemaining > 0) {
                const timeout = setTimeout(() => {
                    refetchUserData();
                    if (location.pathname === "/fight") {
                        toast.error("Battle timed out!");
                    }
                }, millisecondsRemaining);
                return () => clearTimeout(timeout);
            }
        }
    }, [userData?.battleValidUntil, refetchUserData]);

    const getMs = (timestamp: string | bigint | number | null | undefined): number => {
        return getMillisecondsUntilTimestamp(timestamp);
    };

    const isReadyToCollect = (timestamp: string | bigint | number | null | undefined): boolean => {
        return hasTimestampExpired(timestamp);
    };

    useEffect(() => {
        if (craftCollectReady) {
            if (!craftingQueue || craftingQueue.length === 0) {
                setCraftCollectReady(false);
                return;
            }

            const anyReady = craftingQueue.some((craft) => isReadyToCollect(craft?.endsAt));
            if (!anyReady) {
                setCraftCollectReady(false);
            }
        }
    }, [craftingQueue, craftCollectReady, setCraftCollectReady]);

    useEffect(() => {
        const timeouts: NodeJS.Timeout[] = [];
        let needsCollection = false;

        if (craftingQueue) {
            for (const craft of craftingQueue) {
                const millisecondsRemaining = getMs(craft?.endsAt);
                if (millisecondsRemaining > 0) {
                    const timeout = setTimeout(() => {
                        refetchUserData();
                        toast.success("Craft ready to collect!");
                        setCraftCollectReady(true);
                    }, millisecondsRemaining);
                    timeouts.push(timeout);
                } else if (millisecondsRemaining <= 0) {
                    needsCollection = true;
                }
            }
        }
        if (needsCollection && !craftCollectReady) {
            setCraftCollectReady(true);
        }

        return () => {
            timeouts.forEach((timeout) => clearTimeout(timeout));
        };
    }, [craftingQueue, refetchUserData, setCraftCollectReady, craftCollectReady]);
};

export default useTimestampCountdowns;
