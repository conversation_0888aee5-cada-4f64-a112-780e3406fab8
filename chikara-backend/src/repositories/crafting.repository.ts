import { CraftingRecipeModel, ItemModel, TransactionClient, UserCraftingQueueModel, db } from "../lib/db.js";
import type { Prisma } from "@prisma/client";
import { RecipeItemTypes } from "@prisma/client";

type RecipeCreateInput = Prisma.crafting_recipeCreateInput;
type RecipeUpdateInput = Prisma.crafting_recipeUpdateInput;

type UserCraftingQueueCreateInput = Prisma.user_crafting_queueCreateInput;

export const findCraftingRecipeById = async (recipeId: number) => {
    return await db.crafting_recipe.findUnique({
        where: { id: recipeId },
    });
};

export const findCraftingQueueByUserId = async (userId: number) => {
    return await db.user_crafting_queue.findMany({
        where: { userId },
        select: {
            id: true,
            startedAt: true,
            endsAt: true,
            crafting_recipe: {
                select: {
                    id: true,
                    craftTime: true,
                    requiredSkillType: true,
                    requiredSkillLevel: true,
                    cost: true,
                    recipe_item: {
                        select: {
                            count: true,
                            itemType: true,
                            item: {
                                omit: {
                                    createdAt: true,
                                    updatedAt: true,
                                },
                            },
                        },
                    },
                },
            },
        },
    });
};

export const findUserRecipesByUserId = async (userId: number) => {
    return await db.user_recipe.findMany({
        where: { userId },
    });
};

export const findRecipesByLevelAndIds = async (recipeIds: number[]) => {
    return await db.crafting_recipe.findMany({
        select: {
            id: true,
            craftTime: true,
            requiredSkillType: true,
            requiredSkillLevel: true,
            cost: true,
            recipe_item: {
                select: {
                    count: true,
                    itemType: true,
                    item: {
                        omit: {
                            createdAt: true,
                            updatedAt: true,
                        },
                    },
                },
            },
        },
        where: {
            OR: [{ isUnlockable: false }, { id: { in: recipeIds } }],
        },
    });
};

export const findRecipeById = async (recipeId: number) => {
    return await db.crafting_recipe.findUnique({
        where: { id: recipeId },
    });
};

export const createRecipeWithTransaction = async (recipeData: RecipeCreateInput, tx: TransactionClient) => {
    return await tx.crafting_recipe.create({
        data: recipeData,
    });
};

export const addItemToRecipe = async (
    recipe: CraftingRecipeModel,
    itemId: number,
    data: Prisma.recipe_itemCreateInput,
    tx: TransactionClient
) => {
    return await tx.recipe_item.create({
        data: {
            craftingRecipeId: recipe.id,
            itemId,
            count: data.count,
            itemType: data.itemType,
        },
    });
};

export const removeItemFromRecipe = async (recipe: CraftingRecipeModel, item: ItemModel, tx: TransactionClient) => {
    return await tx.recipe_item.delete({
        where: {
            craftingRecipeId_itemId: {
                craftingRecipeId: recipe.id,
                itemId: item.id,
            },
        },
    });
};

export const updateRecipe = async (recipe: CraftingRecipeModel, data: RecipeUpdateInput, tx: TransactionClient) => {
    return await tx.crafting_recipe.update({
        where: { id: recipe.id },
        data: data,
    });
};

export const deleteRecipeItems = async (recipeId: number, tx: TransactionClient) => {
    return await tx.recipe_item.deleteMany({
        where: { craftingRecipeId: recipeId },
    });
};

export const deleteRecipeById = async (recipeId: number) => {
    // Using Prisma transaction to ensure both operations succeed or fail together
    await db.$transaction([
        db.recipe_item.deleteMany({
            where: { craftingRecipeId: recipeId },
        }),
        db.crafting_recipe.delete({
            where: { id: recipeId },
        }),
    ]);
};

export const findCraftingQueueByUserIdAndCraftId = async (userId: number, craftId: number) => {
    return await db.user_crafting_queue.findFirst({
        where: {
            userId,
            id: craftId,
        },
    });
};

export const deleteCraftingQueue = async (craftingQueue: UserCraftingQueueModel) => {
    return await db.user_crafting_queue.delete({
        where: { id: craftingQueue.id },
    });
};

export const createUserCraftingQueue = async (queueData: UserCraftingQueueCreateInput) => {
    return await db.user_crafting_queue.create({
        data: queueData,
    });
};

export const findRecipeItemsByRecipeId = async (recipeId: number, itemType?: RecipeItemTypes) => {
    return await db.recipe_item.findMany({
        where: {
            craftingRecipeId: recipeId,
            ...(itemType && { itemType }),
        },
    });
};
