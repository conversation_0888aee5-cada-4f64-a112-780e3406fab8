/**
 * Utility functions for formatting currency and financial calculations
 */

/**
 * Formats a number as Japanese Yen currency
 * @param amount - The amount to format
 * @param options - Optional formatting options
 * @returns Formatted currency string
 */
export const formatCurrency = (
    amount: number, 
    options: {
        minimumFractionDigits?: number;
        maximumFractionDigits?: number;
        showSymbol?: boolean;
    } = {}
): string => {
    const {
        minimumFractionDigits = 0,
        maximumFractionDigits = 0,
        showSymbol = true,
    } = options;

    if (showSymbol) {
        return new Intl.NumberFormat('ja-JP', {
            style: 'currency',
            currency: 'JPY',
            minimumFractionDigits,
            maximumFractionDigits,
        }).format(amount).replace('JP¥', '¥');
    }

    return new Intl.NumberFormat('ja-JP', {
        minimumFractionDigits,
        maximumFractionDigits,
    }).format(amount);
};

/**
 * Formats a number with compact notation (e.g., 1.2K, 3.4M)
 * @param amount - The amount to format
 * @param showSymbol - Whether to show the yen symbol
 * @returns Formatted currency string with compact notation
 */
export const formatCompactCurrency = (amount: number, showSymbol: boolean = true): string => {
    const formatted = new Intl.NumberFormat('ja-JP', {
        notation: 'compact',
        maximumFractionDigits: 1,
    }).format(amount);

    return showSymbol ? `¥${formatted}` : formatted;
};

/**
 * Calculates a percentage fee from an amount
 * @param amount - The base amount
 * @param feePercentage - The fee percentage (e.g., 5 for 5%)
 * @returns The calculated fee amount
 */
export const calculateFee = (amount: number, feePercentage: number): number => {
    return Math.round((amount / 100) * feePercentage);
};

/**
 * Calculates the total amount including fee
 * @param amount - The base amount
 * @param feePercentage - The fee percentage (e.g., 5 for 5%)
 * @returns The total amount including fee
 */
export const calculateTotalWithFee = (amount: number, feePercentage: number): number => {
    return amount + calculateFee(amount, feePercentage);
};

/**
 * Validates if an amount meets minimum requirements
 * @param amount - The amount to validate
 * @param minimum - The minimum required amount
 * @returns Whether the amount is valid
 */
export const isValidAmount = (amount: number, minimum: number): boolean => {
    return amount >= minimum && amount > 0;
};

/**
 * Formats a percentage for display
 * @param percentage - The percentage value
 * @param decimals - Number of decimal places
 * @returns Formatted percentage string
 */
export const formatPercentage = (percentage: number, decimals: number = 1): string => {
    return `${percentage.toFixed(decimals)}%`;
};

/**
 * Currency display types for different contexts
 */
export type CurrencyType = 'yen' | 'gangCreds' | 'classPoints';

/**
 * Gets the appropriate currency symbol or component for a currency type
 * @param currencyType - The type of currency
 * @returns Currency symbol string or React component props
 */
export const getCurrencySymbol = (currencyType: CurrencyType): string => {
    switch (currencyType) {
        case 'yen':
            return '¥';
        case 'gangCreds':
            return 'GC'; // Fallback text representation
        case 'classPoints':
            return 'CP'; // Fallback text representation
        default:
            return '¥';
    }
};
