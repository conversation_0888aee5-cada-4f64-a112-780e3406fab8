import * as StoryRepository from "../../repositories/story.repository.js";
import * as StoryController from "./story.controller.js";
import { StoryEpisodeContent, StoryEpisodeData } from "./story.types.js";
import { LogErrorStack } from "../../utils/log.js";
import { db } from "../../lib/db.js";

/**
 * Get all story content formatted for admin display
 */
export const getAllStoryContentForAdmin = async () => {
    try {
        // Get all seasons with chapters and episodes with quest integration data
        const seasons = await StoryRepository.findAllSeasons();

        const formattedSeasons = [];

        for (const season of seasons) {
            const chapters = await StoryRepository.findChaptersBySeasonId(season.id);
            const formattedChapters = [];

            for (const chapter of chapters) {
                const episodes = await getEpisodesWithQuestIntegration(chapter.id);

                formattedChapters.push({
                    ...chapter,
                    episodes: episodes.map((episode) => ({
                        ...episode,
                        contentSummary: formatEpisodeContentSummary(episode.content),
                        contentAnalysis: analyzeEpisodeContent(episode.content),
                        sceneCount: episode.content.scenes?.length || 0,
                        hasChoices: !!episode.choices && Object.keys(episode.choices).length > 0,
                    })),
                });
            }

            formattedSeasons.push({
                ...season,
                isUnlocked: true, // Admin view shows all content
                chapters: formattedChapters,
            });
        }

        return formattedSeasons;
    } catch (error) {
        LogErrorStack({ message: "Failed to retrieve story content for admin", error });
        return []; // Return empty array instead of undefined
    }
};

/**
 * Get episodes with quest integration data for admin display
 */
async function getEpisodesWithQuestIntegration(chapterId: number) {
    const episodes = await db.story_episode.findMany({
        where: {
            quest_objective: {
                quest: {
                    chapterId: chapterId,
                },
            },
        },
        include: {
            quest_objective: {
                include: {
                    quest: {
                        include: {
                            quest: true, // Required quest
                            other_quest: true, // Quests that require this one
                        },
                    },
                },
            },
        },
        orderBy: {
            quest_objective: {
                quest: {
                    orderInChapter: "asc",
                },
            },
        },
    });

    return episodes.map((episode) => {
        const quest = episode.quest_objective?.quest;
        const requiredQuest = quest?.quest;
        const triggeringQuests = quest?.other_quest || [];

        return {
            ...episode,
            content: episode.content as StoryEpisodeContent,
            choices: episode.choices as any,
            // Quest integration fields for admin display
            objectiveId: episode.objectiveId,
            questId: quest?.id || null,
            questName: quest?.name || null,
            requiredQuestId: requiredQuest?.id || null,
            requiredQuestName: requiredQuest?.name || null,
            triggersQuestId: triggeringQuests.length > 0 ? triggeringQuests[0].id : null,
            triggersQuestName: triggeringQuests.length > 0 ? triggeringQuests[0].name : null,
            // For now, we don't have a direct "completes quest" relationship
            // This would need to be determined by checking if this is the last objective in a quest
            completesQuestId: null,
            completesQuestName: null,
            // Episode dependencies (not implemented in current schema)
            requiredEpisodeId: null,
            // Explore map integration
            exploreLocation: episode.exploreLocation,
            isActiveNode: true, // For now, assume all episodes are active nodes
        };
    });
}

// ===============================================
// Content Analysis Helpers
// ===============================================

/**
 * Create a summary of episode content for admin overview
 */
function formatEpisodeContentSummary(content: StoryEpisodeContent): string {
    if (!content.scenes || content.scenes.length === 0) {
        return "No content";
    }

    const sceneTypes = content.scenes.map((scene) => scene.type);
    const uniqueTypes = [...new Set(sceneTypes)];
    const speakers = content.scenes.filter((scene) => scene.speaker).map((scene) => scene.speaker);
    const uniqueSpeakers = [...new Set(speakers)];

    let summary = `${content.scenes.length} scenes`;
    if (uniqueTypes.length > 0) {
        summary += ` (${uniqueTypes.join(", ")})`;
    }
    if (uniqueSpeakers.length > 0) {
        summary += ` - Speakers: ${uniqueSpeakers.join(", ")}`;
    }

    return summary;
}

/**
 * Analyze episode content for admin display
 */
function analyzeEpisodeContent(content: StoryEpisodeContent) {
    if (!content.scenes) {
        return {
            totalScenes: 0,
            sceneTypes: [],
            speakers: [],
            totalWordCount: 0,
            estimatedReadingTime: 0,
        };
    }

    const sceneTypes = content.scenes.map((scene) => scene.type);
    const speakers = content.scenes.filter((scene) => scene.speaker).map((scene) => scene.speaker);

    const totalWordCount = content.scenes.reduce((total, scene) => {
        return total + (scene.text?.split(/\s+/).length || 0);
    }, 0);

    // Estimate reading time (average 200 words per minute)
    const estimatedReadingTime = Math.ceil(totalWordCount / 200);

    return {
        totalScenes: content.scenes.length,
        sceneTypes: [...new Set(sceneTypes)],
        speakers: [...new Set(speakers)],
        totalWordCount,
        estimatedReadingTime,
        hasMetadata: !!content.metadata,
        estimatedDuration: content.metadata?.estimatedDuration,
        defaultBackground: content.metadata?.defaultBackground,
    };
}

// ===============================================
// Content Validation Helpers
// ===============================================

/**
 * Validate episode content structure for admin
 */
export const validateEpisodeContent = (content: unknown): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!content) {
        errors.push("Content is required");
        return { isValid: false, errors };
    }

    if (typeof content !== "object" || content === null) {
        errors.push("Content must be an object");
        return { isValid: false, errors };
    }

    const contentObj = content as Record<string, unknown>;

    if (!contentObj.scenes || !Array.isArray(contentObj.scenes)) {
        errors.push("Content must have a scenes array");
    } else {
        // eslint-disable-next-line unicorn/no-array-for-each
        contentObj.scenes.forEach((scene: unknown, index: number) => {
            if (typeof scene !== "object" || scene === null) {
                errors.push(`Scene ${index + 1}: Must be an object`);
                return;
            }

            const sceneObj = scene as Record<string, unknown>;

            if (!sceneObj.id) {
                errors.push(`Scene ${index + 1}: Missing scene ID`);
            }
            if (!sceneObj.type) {
                errors.push(`Scene ${index + 1}: Missing scene type`);
            }
            if (!sceneObj.text) {
                errors.push(`Scene ${index + 1}: Missing scene text`);
            }
        });
    }

    return { isValid: errors.length === 0, errors };
};

/**
 * Get content statistics for admin dashboard
 */
export const getStoryContentStats = async () => {
    try {
        const [seasons, chapters, episodes] = await Promise.all([
            StoryRepository.findAllSeasons(),
            StoryRepository.findAllChapters(),
            StoryRepository.findAllEpisodes(),
        ]);

        const episodesByType = episodes.reduce(
            (acc, episode) => {
                acc[episode.episodeType] = (acc[episode.episodeType] || 0) + 1;
                return acc;
            },
            {} as Record<string, number>
        );

        const totalScenes = episodes.reduce((total, episode) => {
            return total + (episode.content.scenes?.length || 0);
        }, 0);

        return {
            totalSeasons: seasons.length,
            totalChapters: chapters.length,
            totalEpisodes: episodes.length,
            totalScenes,
            episodesByType,
            averageScenesPerEpisode: episodes.length > 0 ? Math.round(totalScenes / episodes.length) : 0,
        };
    } catch (error) {
        LogErrorStack({ message: "Failed to retrieve story content stats", error });
        return {
            totalSeasons: 0,
            totalChapters: 0,
            totalEpisodes: 0,
            totalScenes: 0,
            episodesByType: {},
            averageScenesPerEpisode: 0,
        };
    }
};
