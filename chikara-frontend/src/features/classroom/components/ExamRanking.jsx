import bronzeMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Bronze.png";
import goldMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Gold.png";
import silverMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Silver.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import LoadingState from "@/components/LoadingState";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { BookOpen } from "lucide-react";
import { Link } from "react-router-dom";

const displayGrade = (score, total) => {
    const percent = (score / total) * 100;
    switch (true) {
        case percent === 95:
            return "S";
        case percent >= 90:
            return "A+";
        case percent >= 80:
            return "A";
        case percent >= 65:
            return "B";
        case percent >= 55:
            return "C";
        case percent >= 40:
            return "D";
        case percent >= 30:
            return "E";
        default:
            return "F";
    }
};

const ExamRanking = () => {
    return null; // TODO: Exam rankings need to be re-implemented
    const { isLoading, data } = useQuery({
        queryKey: api.user.getExamRankings.key(),
        staleTime: Number.POSITIVE_INFINITY,
    });

    const rankings = data?.playerResults;
    const totalAvailablePoints = data?.totalAvailablePoints;

    return (
        <section className="mx-auto w-full rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800">
            <div className="flex flex-col gap-2 p-1.5">
                <div className="relative mb-6 flex flex-col text-center text-2xl text-custom-yellow">
                    <p className="-mb-4 mt-2 md:my-0">Exam Rankings</p>
                </div>
                <LoadingState isLoading={isLoading}>
                    {rankings?.map((ranking, i) => (
                        <div
                            key={ranking.playerId}
                            className={cn(
                                "relative flex h-14 gap-1 overflow-hidden rounded-lg border-2 border-black bg-linear-to-r from-blue-700 to-blue-900 p-1"
                            )}
                        >
                            <div className="relative flex h-full w-14 max-w-14 items-center justify-center">
                                {i === 0 && <img className="mx-auto h-11 w-auto" src={goldMedal} alt="Gold medal" />}
                                {i === 1 && (
                                    <img className="mx-auto h-11 w-auto" src={silverMedal} alt="Silver medal" />
                                )}
                                {i === 2 && (
                                    <img className="mx-auto h-11 w-auto" src={bronzeMedal} alt="Bronze medal" />
                                )}
                                {i > 2 && <div className="mx-auto h-12 w-auto"></div>}
                            </div>
                            <div className="relative h-full w-20">
                                <Link to={`/profile/${ranking.playerId}`}>
                                    <DisplayAvatar
                                        src={ranking}
                                        className="mx-auto h-full w-auto rounded-md border-2 border-black"
                                    />
                                </Link>
                            </div>
                            <Link className="" to={`/profile/${ranking.playerId}`}>
                                <div className="flex flex-col justify-center gap-0">
                                    <p className="-mt-0.5 text-left font-body font-semibold text-custom-yellow text-stroke-sm leading-none md:text-xl">
                                        {ranking.username}
                                    </p>

                                    <p className="mt-0.5 text-sm text-white leading-none">Class {ranking.class}</p>
                                </div>
                            </Link>
                            <div className="ml-auto! my-auto mr-5 hidden gap-4 md:flex">
                                <p className="flex gap-1 font-bold font-display text-sm text-stroke-sm text-white leading-none">
                                    <BookOpen />
                                    {ranking?.exam1?.totalCorrectPoints || "N"}/
                                    {ranking?.exam1?.totalAvailablePoints || "A"}
                                </p>
                                <p className="flex gap-1 font-bold font-display text-sm text-stroke-sm text-white leading-none">
                                    <BookOpen />
                                    {ranking?.exam2?.totalCorrectPoints || "N"}/
                                    {ranking?.exam2?.totalAvailablePoints || "A"}
                                </p>

                                <p className="flex gap-1 font-bold font-display text-sm text-stroke-sm text-white leading-none">
                                    <BookOpen />
                                    {ranking?.exam3?.totalCorrectPoints || "N"}/
                                    {ranking?.exam3?.totalAvailablePoints || "A"}
                                </p>
                            </div>
                            <div className="ml-auto! my-auto mr-5 flex gap-6">
                                <p className="-mt-1 font-bold font-display text-3xl text-stroke-sm text-white leading-none">
                                    {(ranking?.exam1?.totalCorrectPoints || 0) +
                                        (ranking?.exam2?.totalCorrectPoints || 0) +
                                        (ranking?.exam3?.totalCorrectPoints || 0)}
                                    /{totalAvailablePoints}
                                </p>
                                <p className="-mt-1 font-bold font-display text-3xl text-custom-yellow text-stroke-sm leading-none">
                                    {displayGrade(
                                        (ranking?.exam1?.totalCorrectPoints || 0) +
                                            (ranking?.exam2?.totalCorrectPoints || 0) +
                                            (ranking?.exam3?.totalCorrectPoints || 0),
                                        totalAvailablePoints
                                    )}
                                </p>
                            </div>
                        </div>
                    ))}
                </LoadingState>
            </div>
        </section>
    );
};
export default ExamRanking;
