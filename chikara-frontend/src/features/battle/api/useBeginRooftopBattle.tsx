import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginRooftopBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.rooftop.beginRooftopBattle.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.battle.getStatus.key() });
                queryClient.invalidateQueries({ queryKey: api.rooftop.rooftopList.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
            },
        })
    );
};

export default useBeginRooftopBattle;
