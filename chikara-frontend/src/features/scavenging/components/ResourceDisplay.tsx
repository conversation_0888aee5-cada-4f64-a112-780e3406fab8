import React from "react";
import { EnhancedResourceInfo } from "../types";
import { getResourceStyles } from "../constants";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { formatCurrency } from "@/utils/currencyHelpers";
interface ResourceDisplayProps {
    itemId: number;
    resourceInfo: EnhancedResourceInfo;
}

const ResourceDisplay: React.FC<ResourceDisplayProps> = ({ itemId, resourceInfo }) => {
    const { count, item } = resourceInfo;
    const styles = getResourceStyles(itemId, item);

    return (
        <div
            className={`flex items-center space-x-2 bg-gray-800 rounded-lg px-2 py-1 border ${"borderColor" in styles ? styles.borderColor : "border-gray-600"}`}
        >
            {item.image ? (
                <img src={displayMissingIcon(item.image)} alt={item.name} className="size-6 object-contain" />
            ) : (
                <span className="text-lg">{styles.emoji}</span>
            )}
            <div className="flex flex-col">
                <span className="text-xs font-medium text-blue-400 truncate max-w-full">{item.name}</span>
                <div className="flex items-center space-x-1">
                    <span className="text-xs text-gray-400">x{count}</span>
                    {item.cashValue && (
                        <span className="text-xs text-green-400">{formatCurrency(item.cashValue * count)}</span>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ResourceDisplay;
