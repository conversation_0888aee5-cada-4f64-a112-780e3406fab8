import type { TrainPetParams, TrainPetResponse } from "@/features/pets/types/pets";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UseTrainPetOptions {
    onSuccess?: (data: TrainPetResponse) => void;
    onError?: (error: Error) => void;
}

const useTrainPet = (options: UseTrainPetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.train.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Train pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default useTrainPet;
