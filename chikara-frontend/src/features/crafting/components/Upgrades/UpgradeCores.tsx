import Button from "@/components/Buttons/Button";
import { DisplayItem } from "@/components/DisplayItem";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { cn } from "@/lib/utils";
import type { InventoryItem, Item, ItemType } from "@/types/item";
import { useMemo } from "react";
import useGetUpgradeItems from "../../api/useGetUpgradeItems";

export default function UpgradeCores({
    selectedUpgradeCores,
    setSelectedUpgradeCores,
    userInventory,
    disabled,
    selectedItemType,
    successRate,
}: {
    selectedUpgradeCores: Record<string, { id: number; count: number }> | null;
    setSelectedUpgradeCores: (selectedUpgradeCores: Record<string, { id: number; count: number }> | null) => void;
    userInventory: InventoryItem[];
    disabled: boolean;
    selectedItemType: ItemType | null;
    successRate: number;
}) {
    const { data: upgradeItems, isLoading } = useGetUpgradeItems({
        staleTime: Number.POSITIVE_INFINITY,
    });

    const nameFilter =
        selectedItemType === "weapon" || selectedItemType === "ranged" ? "Weapon Upgrade Core" : "Armor Upgrade Core";

    const smallCoreItem = upgradeItems?.find((item) => item?.name === `Small ${nameFilter}`) || null;
    const mediumCoreItem = upgradeItems?.find((item) => item?.name === `Medium ${nameFilter}`) || null;
    const largeCoreItem = upgradeItems?.find((item) => item?.name === `Large ${nameFilter}`) || null;
    const giantCoreItem = upgradeItems?.find((item) => item?.name === `Giant ${nameFilter}`) || null;

    const filteredCores = useMemo(() => {
        const smallCores = { item: smallCoreItem, count: 0 };
        const mediumCores = { item: mediumCoreItem, count: 0 };
        const largeCores = { item: largeCoreItem, count: 0 };
        const giantCores = { item: giantCoreItem, count: 0 };

        const cores =
            userInventory?.filter(
                (item) => item?.item?.name.includes(nameFilter) && item?.item?.itemType === "upgrade"
            ) || [];

        cores?.forEach((item) => {
            if (item?.item?.name === smallCoreItem?.name) {
                smallCores.count += item?.count;
            } else if (item?.item?.name === mediumCoreItem?.name) {
                mediumCores.count += item?.count;
            } else if (item?.item?.name === largeCoreItem?.name) {
                largeCores.count += item?.count;
            } else if (item?.item?.name === giantCoreItem?.name) {
                giantCores.count += item?.count;
            }
        });

        return {
            smallCores,
            mediumCores,
            largeCores,
            giantCores,
        };
    }, [smallCoreItem, mediumCoreItem, largeCoreItem, giantCoreItem, userInventory, nameFilter]);

    if (isLoading) return null;

    const hasNoCores = Object.values(filteredCores).every((core) => core.count === 0);

    return (
        <div className="flex flex-col gap-2.5">
            <div
                className={cn(
                    selectedUpgradeCores ? "border-slate-600" : "border-yellow-600",
                    "flex flex-col items-center gap-2 rounded-lg border border-slate-600 bg-slate-900 px-5 pt-2 pb-4 lg:px-8",
                    disabled && "border-slate-600!"
                )}
            >
                <p className={cn("place-content-end text-center text-sm uppercase")}>Upgrade Cores</p>
                <div className="flex gap-4">
                    <CoreSelect
                        text="small"
                        disabled={disabled}
                        coreItem={filteredCores.smallCores}
                        selectedUpgradeCores={selectedUpgradeCores}
                        setSelectedUpgradeCores={setSelectedUpgradeCores}
                    />
                    <CoreSelect
                        text="medium"
                        disabled={disabled}
                        coreItem={filteredCores.mediumCores}
                        selectedUpgradeCores={selectedUpgradeCores}
                        setSelectedUpgradeCores={setSelectedUpgradeCores}
                    />
                    <CoreSelect
                        text="large"
                        disabled={disabled}
                        coreItem={filteredCores.largeCores}
                        selectedUpgradeCores={selectedUpgradeCores}
                        setSelectedUpgradeCores={setSelectedUpgradeCores}
                    />
                    <CoreSelect
                        text="giant"
                        disabled={disabled}
                        coreItem={filteredCores.giantCores}
                        selectedUpgradeCores={selectedUpgradeCores}
                        setSelectedUpgradeCores={setSelectedUpgradeCores}
                    />
                </div>
            </div>

            <div className="flex items-center justify-end gap-4 lg:items-end">
                {/* <Button
  disabled={disabled || hasNoCores}
  onClick={() => (disabled ? null : setSelectedUpgradeCores(filteredCores))}
  className="text-sm! h-10!"
>
  Add all cores
</Button> */}
                <div className="rounded-lg border-gray-600 bg-slate-900 px-12 pt-1 lg:hidden">
                    <p className="text-xs uppercase">Upgrade Chance</p>
                    <p className="text-green-500 text-lg">{(successRate * 100).toFixed(0)}%</p>
                </div>
                <Button
                    variant="destructive"
                    className="text-sm! h-8!"
                    disabled={disabled || hasNoCores}
                    onClick={() => setSelectedUpgradeCores(null)}
                >
                    Reset Cores
                </Button>
            </div>
        </div>
    );
}

const CoreSelect = ({
    coreItem,
    selectedUpgradeCores,
    setSelectedUpgradeCores,
    disabled,
    text,
}: {
    coreItem: { item: Item | null; count: number };
    selectedUpgradeCores: Record<string, { id: number; count: number }> | null;
    setSelectedUpgradeCores: (selectedUpgradeCores: Record<string, { id: number; count: number }> | null) => void;
    disabled: boolean;
    text: string;
}) => {
    if (!coreItem) return null;
    const id = coreItem?.item?.id || 0;
    const inventoryCount = coreItem?.count || 0;

    const currentCount = selectedUpgradeCores?.[text]?.count || 0;
    const isDisabled = disabled || inventoryCount === 0 || currentCount >= inventoryCount;

    const addCore = () => {
        if (inventoryCount === 0) return;
        if (selectedUpgradeCores) {
            const newCores = { ...selectedUpgradeCores };
            if (newCores[text]) {
                if (newCores[text]?.count >= inventoryCount) {
                    return;
                }
                const newCount = newCores[text]?.count + 1;
                newCores[text] = { id, count: newCount };
            } else {
                newCores[text] = { id, count: 1 };
            }

            setSelectedUpgradeCores(newCores);
        } else {
            setSelectedUpgradeCores({ [text]: { id, count: 1 } });
        }
    };

    return (
        <div className={cn("flex flex-col gap-2 text-sm", disabled && "opacity-50")}>
            <div
                className={cn(
                    "gridIcon group gridIconHover col-span-2 flex size-14 cursor-pointer rounded-md bg-slate-900 p-1 text-gray-100 text-stroke-md ring-2 drop-shadow-md hover:bg-slate-600",
                    coreItem !== null && "gridIconHover hover:bg-slate-600",
                    selectedUpgradeCores ? "ring-indigo-600" : "ring-yellow-600",
                    disabled && "ring-slate-600!"
                )}
            >
                {!disabled ? (
                    <div className="relative flex size-full cursor-pointer">
                        <p className="-bottom-1 absolute right-0 z-15 rounded-md bg-black/25 px-1 font-lili text-custom-yellow text-stroke-0">
                            {inventoryCount}
                        </p>

                        <DisplayItem noBackground isClickable className="m-auto size-16" item={coreItem.item} />
                    </div>
                ) : (
                    <div className={cn("m-auto flex select-none flex-col text-center", disabled && "opacity-50")}></div>
                )}
            </div>
            <p>{capitaliseFirstLetter(text)}</p>
            <div className="ml-1 flex items-center justify-center">
                <p className="text-custom-yellow"> {inventoryCount === 0 ? 0 : currentCount}</p>
                <div
                    className={cn(
                        isDisabled ? "border-gray-600 bg-gray-800 opacity-50" : "border-blue-600 bg-blue-900",
                        "mx-auto flex size-7 cursor-pointer items-center justify-center rounded-lg border hover:brightness-110"
                    )}
                    onClick={() => addCore()}
                >
                    +
                </div>
            </div>

            {/* <NumberPicker
              currentValue={0}
              className="ml-2"
              handleInputChange={(e) => console.log(e)}
              maxValue={3}
              minValue={0}
              valueChangeAmount={1}
              textColour="text-custom-yellow"
            /> */}
        </div>
    );
};
