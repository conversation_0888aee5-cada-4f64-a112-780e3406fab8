import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";

interface ClassCountResult {
    class: string;
    user_count: bigint;
}

export const findLeastPopulatedClass = async () => {
    const potentialClasses = ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"];

    try {
        const result = await db.$queryRaw<ClassCountResult[]>(Prisma.sql`
        SELECT
            all_classes.name AS class,
            COUNT(u.id) AS user_count
        FROM
            (
                SELECT 'Honoo' AS name
                UNION ALL SELECT 'Tsuchi'
                UNION ALL SELECT 'Mizu'
                UNION ALL SELECT 'Kaze'
            ) AS all_classes
        LEFT JOIN
            user u ON all_classes.name = u.class
        GROUP BY
            all_classes.name
        ORDER BY
            user_count ASC
        LIMIT 1
      `);

        if (result && result.length > 0 && result[0].class) {
            return result[0].class;
        }

        return potentialClasses[0];
    } catch {
        return potentialClasses[0];
    }
};
