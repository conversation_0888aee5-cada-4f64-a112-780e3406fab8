import { canMakeStateChangesAuth, isLoggedInAuth } from "../../lib/orpc.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as GangController from "./gang.controller.js";
import gangSchema from "./gang.validation.js";
import { handleError } from "../../utils/log.js";

export const gangRouter = {
    // Query endpoints (read-only)
    getGangList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await GangController.gangList(context.user.id);
        return handleResponse(response);
    }),

    getGangInfo: isLoggedInAuth.input(gangSchema.getGangInfo).handler(async ({ input }) => {
        const response = await GangController.getGangInfo(input.gangId);
        return handleResponse(response);
    }),

    getCurrentInvites: isLoggedInAuth.handler(async ({ context }) => {
        const response = await GangController.getUserInviteList(context.user.id);
        return handleResponse(response);
    }),

    getCurrentGang: isLoggedInAuth.handler(async ({ context }) => {
        const response = await GangController.currentGangDetails(context.user.id);
        return handleResponse(response);
    }),

    getGangLogs: isLoggedInAuth.input(gangSchema.getGangLogs).handler(async ({ input, context }) => {
        const response = await GangController.getGangLogs(context.user.id, input.gangId);
        return handleResponse(response);
    }),

    getMemberShares: isLoggedInAuth.input(gangSchema.getMemberShares).handler(async ({ input, context }) => {
        const response = await GangController.getGangMemberShares(context.user.id, input.gangId);
        return handleResponse(response);
    }),

    hasGangSigil: isLoggedInAuth.handler(async ({ context }) => {
        const response = await GangController.hasGangItem(context.user.id);
        return handleResponse(response);
    }),

    getInviteList: isLoggedInAuth.handler(async ({ context }) => {
        if (!context.user.gangId) {
            return handleError("You are not in a gang", 400);
        }
        const response = await GangController.getGangInviteList(context.user.gangId);
        return handleResponse(response);
    }),

    getInviteRequestList: isLoggedInAuth.handler(async ({ context }) => {
        const response = await GangController.getUserInviteRequestList(context.user.id);
        return handleResponse(response);
    }),

    // Mutation endpoints (state changes)
    createGang: canMakeStateChangesAuth.input(gangSchema.createGang).handler(async ({ input, context }) => {
        const response = await GangController.createGang(context.user.id, input);
        return handleResponse(response);
    }),

    inviteMember: canMakeStateChangesAuth.input(gangSchema.inviteMember).handler(async ({ input, context }) => {
        const response = await GangController.inviteToGang(context.user.id, context.user.gangId, input.userId);
        return handleResponse(response);
    }),

    requestInvite: isLoggedInAuth.input(gangSchema.requestInvite).handler(async ({ input, context }) => {
        const response = await GangController.requestGangInvite(context.user.id, { gangId: input.gangId });
        return handleResponse(response);
    }),

    acceptInvite: canMakeStateChangesAuth.input(gangSchema.acceptInvite).handler(async ({ input, context }) => {
        const response = await GangController.acceptGangInvite(context.user.id, input.inviteId);
        return handleResponse(response);
    }),

    declineInvite: canMakeStateChangesAuth.input(gangSchema.declineInvite).handler(async ({ input, context }) => {
        const response = await GangController.declineGangInvite(context.user.id, input.inviteId);
        return handleResponse(response);
    }),

    assignRank: canMakeStateChangesAuth.input(gangSchema.assignRank).handler(async ({ input, context }) => {
        const response = await GangController.assignGangRank(context.user.id, input);
        return handleResponse(response);
    }),

    updatePayoutShares: canMakeStateChangesAuth
        .input(gangSchema.updatePayoutShares)
        .handler(async ({ input, context }) => {
            const response = await GangController.updatePayoutShares(context.user.id, input);
            return handleResponse(response);
        }),

    upgradeHideout: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await GangController.upgradeHideout(context.user.id);
        return handleResponse(response);
    }),

    leaveGang: canMakeStateChangesAuth.handler(async ({ context }) => {
        const response = await GangController.leaveGang(context.user.id);
        return handleResponse(response);
    }),

    kickMember: canMakeStateChangesAuth.input(gangSchema.kickMember).handler(async ({ input, context }) => {
        const response = await GangController.kickMember(context.user.id, input.userId);
        return handleResponse(response);
    }),

    updateGangInfo: canMakeStateChangesAuth.input(gangSchema.updateGangInfo).handler(async ({ input, context }) => {
        const response = await GangController.updateGangInfoORPC(context.user.id, input);
        return handleResponse(response);
    }),
};

export default gangRouter;
