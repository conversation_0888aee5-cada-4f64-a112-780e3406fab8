import { UserModel, db } from "../lib/db.js";
import { Prisma } from "@prisma/client";
import * as UserRepository from "./user.repository.js";

/**
 * Repository function to count users within a specific date range based on a field
 */
export const countUsersInDateRange = async (
    field: keyof UserModel,
    startDate: Date,
    endDate: Date
): Promise<number> => {
    return await db.user.count({
        where: {
            [field]: {
                gte: startDate,
                lte: endDate,
            },
        },
    });
};

/**
 * Repository function to count active users in a time window
 */
export const countActiveUsersInTimeWindow = async (timeWindow: Date) => {
    return await db.user.count({
        where: {
            last_activity: {
                gte: timeWindow,
            },
        },
    });
};

/**
 * Repository function to get total circulating yen for active users
 */
export const getCirculatingYenAmounts = async (timeWindow: Date) => {
    const users = await db.user.findMany({
        where: {
            userType: {
                not: "admin",
            },
            last_activity: {
                gte: timeWindow,
            },
        },
        select: {
            bank_balance: true,
            cash: true,
        },
    });

    const totalYenInBanks = users.reduce((sum, user) => sum + (user.bank_balance || 0), 0);
    const totalYenInCash = users.reduce((sum, user) => sum + (user.cash || 0), 0);

    return { bankYen: totalYenInBanks, cashYen: totalYenInCash };
};

/**
 * Repository function to find inactive high-level players
 */
export const findInactiveHighLevelPlayers = async (cutoffDate: Date, minLevel: number) => {
    const users = await db.user.findMany({
        where: {
            last_activity: {
                lte: cutoffDate,
            },
            level: {
                gte: minLevel,
            },
        },
    });

    return users as UserModel[];
};

/**
 * Repository function to destroy user debuffs
 */
export const destroyUserDebuffs = async (userId: number) => {
    await db.user_status_effect.deleteMany({ where: { userId, effect: { effectType: "DEBUFF" } } });
};

/**
 * Repository function to find user by email
 */
export const findUserByEmail = async (email: string) => {
    const user = await UserRepository.findUserByEmail(email);
    return user as UserModel | null;
};

/**
 * Repository function to update chat message visibility
 */
export const updateChatMessageVisibility = async (messageId: number, hidden: boolean) => {
    await db.chat_message.update({
        where: { id: messageId },
        data: { hidden },
    });
};

/**
 * Repository function to delete a chat message
 */
export const deleteChatMessage = async (messageId: number) => {
    await db.chat_message.delete({ where: { id: messageId } });
};

/**
 * Repository function to hide all user chat messages
 */
export const hideAllUserChatMessages = async (userId: number) => {
    await db.chat_message.updateMany({
        where: { userId },
        data: { hidden: true },
    });
};

/**
 * Repository function to get full gang info
 */
export const getFullGangInfo = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        include: {
            gang_member: {
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            avatar: true,
                        },
                    },
                },
            },
            chat_room: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
    });
};

/**
 * Repository function to count total users
 */
export const countTotalUsers = async () => {
    return await db.user.count();
};

/**
 * Repository function to create a test user
 */
export const createTestUser = async (userData: Prisma.userCreateInput) => {
    return await db.user.create({ data: userData });
};

/**
 * Repository function to get all users with specific attributes
 */
export const getAllUsersWithAttributes = async (attributes: string[]) => {
    return await db.user.findMany({
        select: Object.fromEntries(attributes.map((attr) => [attr, true])),
    });
};

/**
 * Repository function to create an auction item
 */
export const createAuctionItem = async (auctionData: Prisma.auction_itemCreateInput) => {
    return await db.auction_item.create({ data: auctionData });
};

/**
 * Repository function to reset all user roguelike maps
 */
export const resetAllUserRoguelikeMaps = async () => {
    await db.user.updateMany({
        data: {
            roguelikeMap: Prisma.DbNull,
            roguelikeLevel: 1,
        },
    });
};

/**
 * Repository function to find gangs with members
 */
export const findGangsWithMembers = async (gangId: number) => {
    return await db.gang.findMany({
        where: {
            id: gangId,
        },
        include: {
            gang_member: true,
        },
    });
};

/**
 * Repository function to increment user gang credits
 */
export const incrementUserGangCredits = async (userId: number, amount: number): Promise<void> => {
    await db.user.update({
        where: { id: userId },
        data: {
            gangCreds: { increment: amount },
        },
    });
};
