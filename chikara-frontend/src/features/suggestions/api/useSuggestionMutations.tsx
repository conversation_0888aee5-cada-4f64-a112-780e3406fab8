import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Hook to vote on a suggestion
 */
export const useSuggestionVote = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.vote.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getSuggestions.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getVoteHistory.key(),
                });
            },
            onError: (error) => {
                console.error("Suggestion vote error:", error);
                toast.error(error.message || "Failed to vote on suggestion");
            },
        })
    );
};

/**
 * Hook to comment on a suggestion
 */
export const useSuggestionComment = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.comment.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getComments.key(),
                });
                toast.success("Comment added successfully!");
            },
            onError: (error) => {
                console.error("Suggestion comment error:", error);
                toast.error(error.message || "Failed to add comment");
            },
        })
    );
};

/**
 * Hook to create a new suggestion
 */
export const useCreateSuggestion = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.create.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getSuggestions.key(),
                });
                toast.success("Suggestion created successfully!");
            },
            onError: (error) => {
                console.error("Create suggestion error:", error);
                toast.error(error.message || "Failed to create suggestion");
            },
        })
    );
};

/**
 * Hook to change suggestion state (admin only)
 */
export const useChangeSuggestionState = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.changeState.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getSuggestions.key(),
                });
                toast.success("Suggestion state updated!");
            },
            onError: (error) => {
                console.error("Change suggestion state error:", error);
                toast.error(error.message || "Failed to update suggestion state");
            },
        })
    );
};
