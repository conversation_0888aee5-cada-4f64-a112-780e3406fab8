import { api } from "@/helpers/api";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQueryClient } from "@tanstack/react-query";
import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAuthStore, useNormalStore, usePersistStore, useSocketStore } from "./app/store/stores";
import chatMessageAudio from "./assets/sounds/chatMsg.mp3";

// Define types for the chat message
interface ChatMessage {
    userId: number;
    message: string;
    chatRoomId: number;
    // Add other properties that might be in the message object
}

export default function SocketManager() {
    const location = useLocation();
    const { addUnreadChatMessages } = useNormalStore();
    const queryClient = useQueryClient();
    const { muteChat, hideGlobalChat } = usePersistStore();
    const { socket, fetchSocket, setIsSocketConnected } = useSocketStore();
    const authed = useAuthStore((state) => state.authed);
    const isMobile = useCheckMobileScreen();
    const isChatting = location.pathname === "/chat";

    const audio = new Audio(chatMessageAudio);

    function onMessageEvent(message: ChatMessage): void {
        if (
            message?.userId === 0 &&
            message?.message === "The Shrine daily donation goal has been reached. Global buffs are now active!"
        ) {
            queryClient.invalidateQueries({
                queryKey: api.shrine.getActiveBuff.key(),
            });
        }

        queryClient.setQueryData(
            api.chat.getHistory.queryKey({ input: { roomId: message.chatRoomId.toString(), limit: 200 } }),
            (oldData: any) => {
                if (!oldData) return [message];
                const oldMessages = Array.isArray(oldData) ? oldData : [];
                const newArray = [...oldMessages];
                newArray.unshift(message);
                return newArray;
            }
        );

        if (message.chatRoomId === 1 && isMobile && !isChatting) {
            addUnreadChatMessages();
            return;
        } else if (!muteChat && !hideGlobalChat) {
            audio.play();
        }
    }

    useEffect(() => {
        if (socket) {
            socket.on("chat message", onMessageEvent);

            return () => {
                socket.off("chat message", onMessageEvent);
            };
        }
    }, [muteChat, socket, fetchSocket, isChatting, isMobile]);

    useEffect(() => {
        if (socket) {
            const onConnect = (): void => setIsSocketConnected(true);
            const onDisconnect = (): void => setIsSocketConnected(false);
            const onMessagesRemoved = (): void => {
                queryClient.invalidateQueries({
                    queryKey: api.chat.getHistory.queryKey({ input: { roomId: "1", limit: 200 } }),
                });
            };

            socket.on("connect", onConnect);
            socket.on("disconnect", onDisconnect);
            socket.on("messagesRemoved", onMessagesRemoved);

            return () => {
                socket.off("connect", onConnect);
                socket.off("disconnect", onDisconnect);
                socket.off("messagesRemoved", onMessagesRemoved);
            };
        }
    }, [socket, setIsSocketConnected, queryClient]);

    useEffect(() => {
        if (!socket && authed) {
            fetchSocket();
        }
    }, [authed, socket, fetchSocket]);

    return null;
}
