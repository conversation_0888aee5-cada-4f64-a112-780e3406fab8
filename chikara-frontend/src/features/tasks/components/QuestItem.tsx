import { DisplayQuestGiver } from "@/features/tasks/components/DisplayQuestGiver";
import TaskDetails from "@/features/tasks/components/TaskDetails";
import { getObjectiveProgress } from "@/features/tasks/helpers/getObjectiveProgress";
import {
    getStoryQuestBorderStyle,
    getChapterDisplayText,
    getStoryQuestIcon,
} from "@/features/tasks/helpers/storyQuestHelpers";
import { getQuestStatus } from "@/features/tasks/hooks/useQuestSortAndFilter";
import { getObjectiveText } from "@/hooks/useGetQuestObjectiveText";
import { cn } from "@/lib/utils";
import type { InventoryItem } from "@/types/item";
import type { QuestObjectiveWithProgress, QuestWithProgress, QuestGiver } from "@/types/quest";
import { BadgePlus, ChevronDown, ChevronUp, BookOpen } from "lucide-react";
import { type Dispatch, type SetStateAction } from "react";

const getSpecificObjectiveProgress = (quest: QuestWithProgress, objectiveIndex = 0, inventory: InventoryItem[]) => {
    if (!quest.quest_progress || quest.quest_progress.length === 0) {
        return 0;
    }

    const objective = quest.quest_objective?.[objectiveIndex];
    if (!objective) return 0;

    const progressCount = getObjectiveProgress(objective, inventory);

    if (!progressCount) return 0;

    return Math.min(progressCount, objective.quantity || 0);
};

// Calculate overall quest progress across all objectives
const getOverallQuestProgress = (quest: QuestWithProgress, inventory: InventoryItem[]) => {
    if (!quest.quest_objective || quest.quest_objective.length === 0) {
        return {
            currentProgress: 0,
            totalRequired: 0,
            percentage: 0,
        };
    }

    let totalProgress = 0;
    let totalRequired = 0;

    quest.quest_objective.forEach((objective: QuestObjectiveWithProgress, index: number) => {
        totalRequired += objective.quantity || 0;
        totalProgress += getSpecificObjectiveProgress(quest, index, inventory);
    });

    return {
        currentProgress: totalProgress,
        totalRequired,
        percentage: totalRequired > 0 ? (totalProgress / totalRequired) * 100 : 0,
    };
};

const toggleQuestExpand = (
    questId: number,
    expandedQuests: number[],
    setExpandedQuests: Dispatch<SetStateAction<number[]>>
) => {
    if (expandedQuests.includes(questId)) {
        setExpandedQuests(expandedQuests.filter((id) => id !== questId));
    } else {
        setExpandedQuests([...expandedQuests, questId]);
    }
};

interface QuestItemProps {
    quest: QuestWithProgress;
    questGivers: QuestGiver[];
    availableQuests?: QuestWithProgress[];
    inventory?: InventoryItem[];
    expandedQuests: number[];
    setExpandedQuests: Dispatch<SetStateAction<number[]>>;
    isPinned?: boolean;
}

export default function QuestItem({
    quest,
    questGivers,
    availableQuests = [],
    inventory = [],
    expandedQuests,
    setExpandedQuests,
    isPinned = false,
}: QuestItemProps) {
    const status = getQuestStatus(quest);
    const isExpanded = expandedQuests.includes(quest.id);
    const hasMultipleObjectives = quest.quest_objective?.length > 1;
    const primaryObjective = quest.quest_objective?.[0];
    const {
        currentProgress,
        totalRequired,
        percentage: progressPercentage,
    } = getOverallQuestProgress(quest, inventory);

    const questGiver = questGivers?.find((giver) => giver.id === quest.shopId) || questGivers?.[0];

    const chapterText = getChapterDisplayText(quest);
    const storyIcon = getStoryQuestIcon(quest, status);
    const storyBorderStyle = getStoryQuestBorderStyle(quest, status);

    return (
        <div
            key={quest.id}
            className={cn(
                "bg-gray-800/50 rounded-lg border transition-colors",
                quest.isStoryQuest && storyBorderStyle
                    ? storyBorderStyle
                    : isPinned
                      ? "border-amber-600/50"
                      : status === "available"
                        ? "border-yellow-700/50"
                        : status === "in_progress"
                          ? "border-blue-700/50"
                          : status === "ready_to_complete"
                            ? "border-purple-700/50"
                            : "border-green-700/50"
            )}
        >
            {/* Quest Header */}
            <div
                className="p-2 flex items-center justify-between cursor-pointer"
                onClick={() => toggleQuestExpand(quest.id, expandedQuests, setExpandedQuests)}
            >
                <div className="flex items-center gap-2">
                    {/* Quest Giver Avatar */}
                    <div className="relative">
                        <div className="size-8 rounded-full bg-linear-to-br from-gray-700 to-gray-900 flex items-center justify-center overflow-hidden border border-gray-600">
                            <DisplayQuestGiver src={questGiver} width={32} height={32} />
                        </div>
                    </div>

                    <div className="flex-1">
                        <div className="flex items-center gap-1.5">
                            {quest.isStoryQuest && storyIcon && <span className="text-sm">{storyIcon}</span>}
                            <h4
                                className={cn(
                                    "text-sm font-medium font-display",
                                    quest.isStoryQuest ? "text-amber-300" : "text-custom-yellow"
                                )}
                            >
                                {quest.name}
                            </h4>
                            {quest.isStoryQuest && <BookOpen className="size-3 text-amber-400" />}
                        </div>
                        <div className="flex items-center gap-1 text-xs">
                            {quest.isStoryQuest && chapterText ? (
                                <span className="text-amber-400 font-medium">{chapterText}</span>
                            ) : (
                                <span className="text-blue-400">{questGiver?.name}</span>
                            )}
                            {quest.levelReq > 1 && (
                                <>
                                    <span className="text-gray-500">•</span>
                                    <span className="text-gray-300">Lvl {quest.levelReq}+</span>
                                </>
                            )}
                        </div>
                    </div>
                </div>

                <div className="flex items-center gap-2">
                    {/* New Quests */}
                    <div className="flex -space-x-1">
                        {availableQuests?.includes(quest) && (
                            <div className="size-5 rounded-full bg-yellow-900/30 border border-yellow-700/50 flex items-center justify-center">
                                <BadgePlus className="size-3 text-yellow-500" />
                            </div>
                        )}
                    </div>

                    {isExpanded ? (
                        <ChevronUp className="size-5 text-gray-400" />
                    ) : (
                        <ChevronDown className="size-5 text-gray-400" />
                    )}
                </div>
            </div>

            {/* Progress Bar (for in-progress quests) */}
            {status === "in_progress" && !isExpanded && (
                <div className="px-2 pb-2">
                    <div className="flex justify-between text-xs mb-1">
                        {hasMultipleObjectives ? (
                            <>
                                <span className="text-gray-300">Multiple Objectives</span>
                                <span className="text-gray-300">
                                    {currentProgress}/{totalRequired}
                                </span>
                            </>
                        ) : (
                            <>
                                <span className="text-gray-300">
                                    {primaryObjective ? getObjectiveText(primaryObjective) : "No objectives"}
                                </span>
                                <span className="text-gray-300">
                                    {getSpecificObjectiveProgress(quest, 0, inventory)}/
                                    {primaryObjective?.quantity || 0}
                                </span>
                            </>
                        )}
                    </div>
                    <div className="w-full h-1.5 bg-gray-800 rounded-full overflow-hidden">
                        <div
                            className="h-full bg-blue-600 rounded-full"
                            style={{ width: `${progressPercentage}%` }}
                        ></div>
                    </div>
                </div>
            )}

            {/* Quest Details (Expanded) */}
            {isExpanded && (
                <TaskDetails
                    quest={quest}
                    objectives={quest.quest_objective}
                    overallProgress={{
                        currentProgress,
                        totalRequired,
                        progressPercentage,
                    }}
                />
            )}
        </div>
    );
}
