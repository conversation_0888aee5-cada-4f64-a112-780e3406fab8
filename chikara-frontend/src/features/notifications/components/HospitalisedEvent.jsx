import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Flame } from "lucide-react";

import { Link } from "react-router-dom";
import { formatCurrency } from "@/utils/currencyHelpers";

function HospitalisedEvent({ details, read }) {
    const { data: attackedInfo } = useGetUserInfo(details.attacked, {
        enabled: !!details.attacked,
    });

    const { data: attackerInfo } = useGetUserInfo(details.attackerId, {
        enabled: !!details.attackerId,
    });

    const attackerData = attackedInfo || attackerInfo;

    const renderHospitalisedEventText = (e) => {
        if (e.reason && e.reason === "battle") {
            return (
                <div className="dark:text-gray-50">
                    You were defeated in battle by{" "}
                    <Link className="text-blue-600" to={`/profile/${e.attackerId}`}>
                        {attackerData?.username}
                    </Link>
                    {details?.injury ? (
                        <InjuryText injuryName={details.injury} injuryTier={details.injuryTier} />
                    ) : (
                        <p className="text-red-200">You got away with no injuries</p>
                    )}
                </div>
            );
        }
        switch (e.action) {
            case "mug":
                return (
                    <p className="dark:text-gray-50">
                        {e.attackerId === 0 ? (
                            <span className="text-black dark:text-slate-200 dark:text-stroke-sm">Anonymous</span>
                        ) : (
                            <Link className="text-blue-600" to={`/profile/${e.attackerId}`}>
                                {e.attackerName}
                            </Link>
                        )}{" "}
                        mugged you for{" "}
                        <span className="text-indigo-800 dark:text-indigo-400">{formatCurrency(e.mugAmount)}</span>{" "}
                        {details?.injury && <InjuryText injuryName={details.injury} injuryTier={details.injuryTier} />}
                    </p>
                );
            case "cripple":
                return (
                    <div className="dark:text-gray-50">
                        {e.attackerId === 0 ? (
                            <span className="text-black dark:text-slate-200 dark:text-stroke-sm">Anonymous</span>
                        ) : (
                            <Link className="text-blue-600" to={`/profile/${e.attackerId}`}>
                                {e.attackerName}
                            </Link>
                        )}{" "}
                        {details?.wasBitten
                            ? " defeated you in a fight and knocked you unconscious. You awaken with a terrible fever and bite marks on your arm."
                            : " crippled you in a fight."}
                        {details?.injury && !details?.wasBitten && (
                            <InjuryText injuryName={details.injury} injuryTier={details.injuryTier} />
                        )}
                    </div>
                );
            case "leave":
                return (
                    <div className="dark:text-gray-50">
                        {e.attackerId === 0 ? (
                            <span className="text-black dark:text-slate-200 dark:text-stroke-sm">Anonymous</span>
                        ) : (
                            <Link className="text-blue-600" to={`/profile/${e.attackerId}`}>
                                {e.attackerName}
                            </Link>
                        )}{" "}
                        defeated you in a fight.
                        {details?.injury && <InjuryText injuryName={details.injury} injuryTier={details.injuryTier} />}
                    </div>
                );
            default:
                if (e.reason === "character_encounter") {
                    return (
                        <div className="dark:text-gray-50">
                            You were injured by <span className="text-sky-400">{e?.dialogue?.character}</span> in
                            Streets {e?.injury && <InjuryText injuryName={e.injury} injuryTier={e.injuryTier} />}
                        </div>
                    );
                }
                return (
                    <div>
                        You lost a fight against {e.name} in Streets{" "}
                        {details?.injury && <InjuryText injuryName={details.injury} injuryTier={details.injuryTier} />}
                    </div>
                );
        }
    };

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                {renderHospitalisedEventText(details)}
            </td>
        </>
    );
}

const InjuryText = ({ injuryName, injuryTier }) => {
    const injuryTierColor = (tier) => {
        switch (tier) {
            case "Minor":
                return "text-red-400";
            case "Moderate":
                return "text-red-500";
            case "Severe":
                return "text-red-600";
            default:
                return "text-red-400";
        }
    };
    return (
        <p className="">
            You received a <span className={injuryTierColor(injuryTier)}>{injuryTier}</span>{" "}
            <span className="text-orange-500">{injuryName}</span> injury
        </p>
    );
};

export default HospitalisedEvent;
