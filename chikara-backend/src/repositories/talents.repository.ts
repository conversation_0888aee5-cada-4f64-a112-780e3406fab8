import { db } from "../lib/db.js";

export const decrementUserTalentPoints = async (userId: number, pointsToDecrement: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { talentPoints: { decrement: pointsToDecrement } },
    });
};

export const incrementUserMaxActionPoints = async (userId: number, increment: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { maxActionPoints: { increment } },
    });
};

export const incrementUserHealth = async (userId: number, healthIncrement: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { health: { increment: healthIncrement } },
    });
};

/**
 * Finds all talents for a given user
 */
export const findUserTalents = async (userId: number) => {
    return await db.user_talent.findMany({
        where: { userId: userId },
        select: { userId: true, talentId: true, level: true },
    });
};

/**
 * Finds a specific talent for a user
 */
export const findUserTalent = async (userId: number | string, talentId: number) => {
    if (typeof userId !== "number") {
        userId = Number.parseInt(userId);
    }

    return await db.user_talent.findFirst({
        where: {
            userId,
            talentId: talentId,
        },
    });
};

/**
 * Creates a new user talent entry
 */
export const createUserTalent = async (userId: number, talentId: number) => {
    return await db.user_talent.create({
        data: {
            userId: userId,
            talentId: talentId,
            level: 1,
        },
    });
};

/**
 * Updates an existing user talent level
 */
export const updateUserTalentLevel = async (userId: number, talentId: number, newLevel: number) => {
    return await db.user_talent.update({
        where: {
            talentId_userId: {
                userId: userId,
                talentId: talentId,
            },
        },
        data: {
            level: newLevel,
        },
    });
};

/**
 * Deletes all talents for a user
 */
export const deleteAllUserTalents = async (userId: number) => {
    return await db.user_talent.deleteMany({
        where: {
            userId: userId,
        },
    });
};

/**
 * Updates user talent points
 */
export const updateUserTalentPoints = async (userId: number, talentPoints: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { talentPoints },
    });
};

/**
 * Updates user max action points
 */
export const updateUserMaxActionPoints = async (userId: number, maxActionPoints: number) => {
    return await db.user.update({
        where: { id: userId },
        data: { maxActionPoints },
    });
};

/**
 * Updates user equipped abilities
 */
export const resetUserEquippedAbilities = async (userId: number) => {
    return await db.user_equipped_abilities.upsert({
        where: { userId },
        update: {
            equippedAbility1Id: null,
            equippedAbility2Id: null,
            equippedAbility3Id: null,
            equippedAbility4Id: null,
        },
        create: {
            userId,
            equippedAbility1Id: null,
            equippedAbility2Id: null,
            equippedAbility3Id: null,
            equippedAbility4Id: null,
        },
    });
};

export const findUserEquippedAbilities = async (userId: number) => {
    return await db.user_equipped_abilities.findUnique({
        where: { userId },
        include: {
            talent_equippedAbility1: true,
            talent_equippedAbility2: true,
            talent_equippedAbility3: true,
            talent_equippedAbility4: true,
        },
    });
};

export const updateUserEquippedAbilities = async (userId: number, slot: number, talentId: number | null) => {
    if (slot < 1 || slot > 4) {
        throw new Error(`Invalid slot number: ${slot}. Must be between 1 and 4.`);
    }
    const equippedAbilityId = `equippedAbility${slot}Id`;
    return await db.user_equipped_abilities.upsert({
        where: { userId },
        update: { [equippedAbilityId]: talentId },
        create: {
            userId,
            equippedAbility1Id: slot === 1 ? talentId : null,
            equippedAbility2Id: slot === 2 ? talentId : null,
            equippedAbility3Id: slot === 3 ? talentId : null,
            equippedAbility4Id: slot === 4 ? talentId : null,
        },
    });
};
