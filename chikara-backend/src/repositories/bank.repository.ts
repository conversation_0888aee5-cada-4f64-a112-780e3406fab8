import { db } from "../lib/db.js";
import type { Prisma } from "@prisma/client";

export const createBankTransaction = async (data: Prisma.bank_transactionCreateInput) => {
    return await db.bank_transaction.create({
        data,
    });
};

export const getTransactionHistory = async (userId: number, limit: number) => {
    return await db.bank_transaction.findMany({
        select: {
            id: true,
            transaction_type: true,
            cash: true,
            transactionFee: true,
            createdAt: true,
            initiatorCashBalance: true,
            initiatorBankBalance: true,
            secondPartyCashBalance: true,
            secondPartyBankBalance: true,
            initiatorId: true,
            secondPartyId: true,
        },
        orderBy: {
            createdAt: "desc",
        },
        take: limit,
        where: {
            OR: [{ initiatorId: userId }, { secondPartyId: userId }],
            transaction_type: {
                in: ["bank_deposit", "bank_withdrawl", "bank_transfer"],
            },
        },
    });
};

export const updateBothUsersTransaction = async (
    userId: number,
    recipientId: number,
    transferAmountNum: number,
    transactionFee: number
) => {
    return await db.$transaction([
        db.user.update({
            where: { id: userId },
            data: {
                bank_balance: {
                    decrement: transferAmountNum,
                },
            },
        }),
        db.user.update({
            where: { id: recipientId },
            data: {
                bank_balance: {
                    increment: transferAmountNum - transactionFee,
                },
            },
        }),
    ]);
};
