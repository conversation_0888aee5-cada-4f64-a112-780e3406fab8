import Button from "@/components/Buttons/Button";
import InlineAlert, { type AlertType } from "@/components/InlineAlert";
import { Callout } from "@/components/TestComponents/Callout";
import { useBankDeposit } from "@/features/bank/api/useBankDeposit";
import { useBankTransfer } from "@/features/bank/api/useBankTransfer";
import { useBankWithdraw } from "@/features/bank/api/useBankWithdraw";
import TransactionHistory from "@/features/bank/components/TransactionHistory";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { formatCurrency, getCurrencySymbol } from "@/utils/currencyHelpers";

type TransactionType = "Deposit" | "Withdraw" | "Transfer";

export default function Bank() {
    const [transactionAmount, setTransactionAmount] = useState(0);
    const [transactionType, setTransactionType] = useState<TransactionType>("Deposit");
    const [transactionAlertType, setTransactionAlertType] = useState<AlertType | undefined>(undefined);
    const [transferID, setTransferID] = useState(0);
    const [alertAmount, setAlertAmount] = useState(0);
    const { data: currentUser } = useFetchCurrentUser();

    // ORPC mutation hooks
    const depositMutation = useBankDeposit();
    const withdrawMutation = useBankWithdraw();
    const transferMutation = useBankTransfer();

    const {
        MINIMUM_WITHDRAWAL,
        MINIMUM_DEPOSIT,
        MINIMUM_TRANSFER,
        TRANSACTION_HISTORY_LIMIT,
        TRANSACTION_FEE,
        BANK_DISABLED,
        DEPOSIT_DISABLED,
    } = useGameConfig();
    const BANK_TRANSACTION_FEE = TRANSACTION_FEE * 100;

    if (BANK_DISABLED) {
        return (
            <div className="mt-10 flex flex-col dark:text-slate-200">
                <div className="mx-auto text-center">
                    <h2 className="text-xl">Bank currently Disabled</h2>
                    <p>Please return later.</p>
                </div>
            </div>
        );
    }

    if (DEPOSIT_DISABLED && transactionType === "Deposit") {
        setTransactionType("Withdraw");
    }

    const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setTransactionAmount(0);
        setTransactionType(e.target.value as TransactionType);
    };

    const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setTransactionAmount(Number.parseInt(e.target.value));
    };

    const handleSetMaxAmount = (e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        if (transactionType === "Deposit") {
            setTransactionAmount(currentUser?.cash ?? 0);
            return;
        }
        setTransactionAmount(currentUser?.bank_balance ?? 0);
    };

    const handleIDChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setTransferID(Number.parseInt(e.target.value));
    };

    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        bankAction();
    };

    async function bankAction() {
        if (transactionType === "Deposit") {
            if (transactionAmount < MINIMUM_DEPOSIT) {
                setTransactionAlertType("DepositAmountTooLow");
                return;
            }
        }

        if (transactionType === "Withdraw") {
            if (transactionAmount < MINIMUM_WITHDRAWAL) {
                setTransactionAlertType("WithdrawAmountTooLow");
                return;
            }
        }

        if (transactionType === "Transfer") {
            if (transactionAmount < MINIMUM_TRANSFER) {
                setTransactionAlertType("TransferAmountTooLow");
                return;
            }
            if (currentUser?.id === transferID) {
                toast.error("You can't bank transfer to yourself!");
                return;
            }
        }

        try {
            if (transactionType === "Deposit") {
                await depositMutation.mutateAsync({
                    amount: transactionAmount,
                });
            } else if (transactionType === "Withdraw") {
                await withdrawMutation.mutateAsync({
                    amount: transactionAmount,
                });
            } else if (transactionType === "Transfer") {
                await transferMutation.mutateAsync({
                    recipientId: transferID,
                    transferAmount: transactionAmount,
                });
            }

            setTransactionAlertType(transactionType);
            setAlertAmount(transactionAmount);
            setTransactionAmount(0);
        } catch (error) {
            console.error(error);

            if (error instanceof Error && error.message.includes("400")) {
                if (transactionType === "Deposit") {
                    setTransactionAlertType("InvalidDepositAmount");
                } else if (transactionType === "Withdraw") {
                    setTransactionAlertType("InvalidWithdrawAmount");
                }
            }
            toast.error(error instanceof Error ? error.message : "An unknown error occurred");
        }
    }

    return (
        <div className="mb-6 flex flex-col justify-center md:mx-auto md:mb-0 md:max-w-6xl">
            {DEPOSIT_DISABLED && (
                <Callout
                    title="Due to a recent security breach, all bank deposits are temporarily closed."
                    className="mx-auto! w-fit! pr-4!"
                ></Callout>
            )}

            <div className="mx-auto my-4 flex w-full flex-col border-y bg-white p-4 md:w-auto md:rounded-md md:border dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300">
                <div className="mx-auto">
                    <p>Current Bank Balance: {formatCurrency(currentUser?.bank_balance || 0)}</p>
                    <p>Current Cash Balance: {formatCurrency(currentUser?.cash || 0)}</p>
                </div>
                <div className="text-center sm:flex-auto">
                    <p className="mt-4 text-sm ">
                        You will be charged a {BANK_TRANSACTION_FEE}% transaction fee for all deposits and transfers.
                    </p>
                    <p className="mt-2 text-sm ">Money in your bank cannot be mugged from you.</p>
                </div>
                <form className="mx-auto mt-5 flex flex-col" onSubmit={handleSubmit}>
                    <label htmlFor="location" className="block font-medium text-sm ">
                        Transaction Type
                    </label>
                    <select
                        id="location"
                        name="location"
                        className="mt-1 block w-full rounded-md border-gray-600 bg-gray-900 py-2 pr-10 pl-3 font-body font-semibold text-base focus:border-indigo-500 focus:outline-hidden focus:ring-indigo-500 sm:text-sm dark:text-gray-300"
                        value={transactionType}
                        onChange={handleTypeChange}
                    >
                        {DEPOSIT_DISABLED ? null : (
                            <option className="font-semibold" value="Deposit">
                                Deposit
                            </option>
                        )}

                        <option className="font-semibold" value="Withdraw">
                            Withdraw
                        </option>
                        <option className="font-semibold" value="Transfer">
                            Bank Transfer
                        </option>
                    </select>
                    <div className="mt-3 mb-5">
                        <label htmlFor="amount" className="block font-medium text-sm ">
                            Amount
                        </label>
                        <div className="mt-1 flex gap-3 rounded-md shadow-xs">
                            <div className="flex">
                                <span className="inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm">
                                    {getCurrencySymbol("yen")}
                                </span>
                                <input
                                    type="number"
                                    name="amount"
                                    id="amount"
                                    className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300"
                                    placeholder="0"
                                    min="0"
                                    value={transactionAmount}
                                    onChange={handleAmountChange}
                                />
                            </div>
                            <Button variant="flat" size="md" onClick={handleSetMaxAmount}>
                                Max
                            </Button>
                        </div>
                    </div>
                    {transactionType === "Transfer" && (
                        <div className="mt-3 mb-5">
                            <label htmlFor="amount" className="block font-medium text-sm ">
                                Recipient Student ID
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <span className="inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-900 px-3 text-gray-300 text-shadow sm:text-sm">
                                    #
                                </span>
                                <input
                                    type="number"
                                    name="amount"
                                    id="amount"
                                    className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300"
                                    placeholder="0"
                                    onChange={handleIDChange}
                                />
                            </div>
                        </div>
                    )}
                    <Button fullWidth className="mx-auto max-w-36 text-stroke-sm dark:text-slate-100">
                        Confirm
                    </Button>
                </form>
            </div>
            <div className="mb-2 h-16">
                {transactionAlertType && (
                    <InlineAlert
                        transactionType={transactionAlertType}
                        amount={alertAmount}
                        transferID={transferID}
                        TRANSACTION_FEE={BANK_TRANSACTION_FEE}
                        MINIMUM_WITHDRAWAL={MINIMUM_WITHDRAWAL}
                        MINIMUM_DEPOSIT={MINIMUM_DEPOSIT}
                        MINIMUM_TRANSFER={MINIMUM_TRANSFER}
                    />
                )}
            </div>

            <TransactionHistory historyLimit={TRANSACTION_HISTORY_LIMIT} />
        </div>
    );
}
