import type { GameConfig } from "../../hooks/useGameConfig";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import { JSONParse, JSONStringify } from "json-with-bigint";

interface PersistState {
    gameConfig: GameConfig | null;
    setGameConfig: (gameConfig: GameConfig) => void;
    serverVersion: string | null;
    setServerVersion: (serverVersion: string) => void;
    colourTheme: string;
    setColourTheme: (colourTheme: string) => void;
    email: string;
    setEmail: (email: string) => void;
    muteChat: boolean;
    setMuteChat: (muteChat: boolean) => void;
    hideGlobalChat: boolean;
    setHideGlobalChat: (hideGlobalChat: boolean) => void;
    twelveHrClock: boolean;
    setTwelveHrClock: (twelveHrClock: boolean) => void;
    keyboardShortcutsEnabled: boolean;
    setKeyboardShortcutsEnabled: (keyboardShortcutsEnabled: boolean) => void;
    explorePageSetting: "map" | "list" | "travel";
    setExplorePageSetting: (explorePageSetting: "map" | "list" | "travel") => void;
    updateBannerVisible: boolean;
    setUpdateBannerVisible: (updateBannerVisible: boolean) => void;
    staleCurrentUserData: any | null;
    setStaleCurrentUserData: (staleCurrentUserData: any) => void;
    marketTablePageSize: number;
    setMarketTablePageSize: (marketTablePageSize: number) => void;
    persistedTablePageSize: number;
    setPersistedTablePageSize: (persistedTablePageSize: number) => void;
    persistedZone: any | null;
    setPersistedZone: (persistedZone: any) => void;
    zombieEffectsDisabled: boolean;
    setZombieEffectsDisabled: (zombieEffectsDisabled: boolean) => void;
    endingAnimationPlayed: boolean;
    setEndingAnimationPlayed: (endingAnimationPlayed: boolean) => void;
    alphaDisclaimerComplete: boolean;
    setAlphaDisclaimerComplete: (alphaDisclaimerComplete: boolean) => void;
}

export const persistStore = create<PersistState>()(
    persist(
        (set) => ({
            gameConfig: null,
            setGameConfig: (gameConfig) => set(() => ({ gameConfig })),
            serverVersion: null,
            setServerVersion: (serverVersion) => set(() => ({ serverVersion })),
            colourTheme: "dark",
            setColourTheme: (colourTheme) => set(() => ({ colourTheme })),
            email: "",
            setEmail: (email) => set(() => ({ email })),
            muteChat: false,
            setMuteChat: (muteChat) => set(() => ({ muteChat })),
            hideGlobalChat: false,
            setHideGlobalChat: (hideGlobalChat) => set(() => ({ hideGlobalChat })),
            twelveHrClock: true,
            setTwelveHrClock: (twelveHrClock) => set(() => ({ twelveHrClock })),
            keyboardShortcutsEnabled: true,
            setKeyboardShortcutsEnabled: (keyboardShortcutsEnabled) => set(() => ({ keyboardShortcutsEnabled })),
            explorePageSetting: "list",
            setExplorePageSetting: (explorePageSetting) => set(() => ({ explorePageSetting })),
            updateBannerVisible: false,
            setUpdateBannerVisible: (updateBannerVisible) => set(() => ({ updateBannerVisible })),
            staleCurrentUserData: null,
            setStaleCurrentUserData: (staleCurrentUserData) => set(() => ({ staleCurrentUserData })),
            marketTablePageSize: 10,
            setMarketTablePageSize: (marketTablePageSize) => set(() => ({ marketTablePageSize })),
            persistedTablePageSize: 10,
            setPersistedTablePageSize: (persistedTablePageSize) => set(() => ({ persistedTablePageSize })),
            persistedZone: null,
            setPersistedZone: (persistedZone) => set(() => ({ persistedZone })),
            zombieEffectsDisabled: false,
            setZombieEffectsDisabled: (zombieEffectsDisabled) => set(() => ({ zombieEffectsDisabled })),
            endingAnimationPlayed: false,
            setEndingAnimationPlayed: (endingAnimationPlayed) => set(() => ({ endingAnimationPlayed })),
            alphaDisclaimerComplete: false,
            setAlphaDisclaimerComplete: (alphaDisclaimerComplete) => set(() => ({ alphaDisclaimerComplete })),
        }),
        {
            name: "persistStorage", // name of item in the storage (must be unique)
            storage: createJSONStorage(() => localStorage, {
                replacer: (_key, value) => JSONStringify(value),
                reviver: (_key, value) => JSONParse(value),
            }),
            version: 1.2,
        }
    )
);
