export class ApiError extends Error {
    public status?: number;
    public originalError?: any;

    constructor(message: string, status?: number, originalError?: any) {
        super(message);
        this.name = "ApiError";
        this.status = status;
        this.originalError = originalError;
    }
}

export class UnauthorizedError extends ApiError {
    constructor(message = "Your session has expired. Please log in again.") {
        super(message, 401);
        this.name = "UnauthorizedError";
    }
}

export class MaintenanceModeError extends ApiError {
    constructor(message = "The system is currently undergoing maintenance.") {
        super(message); // Status might not be relevant or consistent here
        this.name = "MaintenanceModeError";
    }
}

export class ForbiddenError extends ApiError {
    constructor(message = "Your session has expired. Please log in again.") {
        super(message, 403);
        this.name = "ForbiddenError";
    }
}
