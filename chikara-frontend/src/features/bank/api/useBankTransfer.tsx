import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBankTransfer = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.bank.transfer.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.bank.getBankTransactions.key(),
                });
            },
        })
    );
};
