import { cn } from "@/lib/utils";
import { Banknote, CheckCircle, XCircle, Award } from "lucide-react";
import type { UserStat } from "@/types/user";
import { formatCurrency } from "@/utils/currencyHelpers";

interface RankCardProps {
    rank: Record<UserStat, number>;
    title: string;
    level: number;
    salary: number;
    accent?: "current" | "next" | "future";
    getModifiedSalary: (_salary: number) => number;
    getStatRequirements: () => { type: UserStat; name: string }[];
    checkIfMeetsStatReqs: (_statName: UserStat, _statReqValue: number) => boolean;
}

export default function RankCard({
    rank,
    title,
    level,
    salary,
    accent = "current",
    getModifiedSalary,
    getStatRequirements,
    checkIfMeetsStatReqs,
}: RankCardProps) {
    const statReq = getStatRequirements();
    const accentColors = {
        current: "from-slate-700 to-slate-900 border-slate-600",
        next: "from-indigo-900/50 to-slate-900 border-indigo-600",
        future: "from-purple-900/50 to-slate-900 border-purple-600",
    };

    const iconColors = {
        current: "text-slate-400",
        next: "text-indigo-400",
        future: "text-purple-400",
    };

    return (
        <div
            className={cn(
                "relative overflow-hidden rounded-xl border bg-gradient-to-b p-6 shadow-lg transition-all hover:shadow-xl",
                accentColors[accent]
            )}
        >
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
                <div
                    className="h-full w-full"
                    style={{
                        backgroundImage: `repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.1) 35px, rgba(255,255,255,.1) 70px)`,
                    }}
                />
            </div>

            {/* Content */}
            <div className="relative z-10">
                {/* Header */}
                <div className="mb-4 flex items-center justify-between">
                    <Award className={cn("size-8", iconColors[accent])} />
                    <span
                        className={cn(
                            "rounded-full px-3 py-1 text-xs font-semibold uppercase tracking-wider",
                            accent === "current" && "bg-slate-700 text-slate-300",
                            accent === "next" && "bg-indigo-900/50 text-indigo-300",
                            accent === "future" && "bg-purple-900/50 text-purple-300"
                        )}
                    >
                        {title}
                    </span>
                </div>

                {/* Rank & Salary */}
                <div className="mb-4">
                    <h3 className="text-2xl font-bold text-white">Rank {level}</h3>
                    <div className="mt-2 flex items-center text-custom-yellow">
                        <Banknote className="mr-2 size-5" />
                        <span className="text-xl font-semibold">{formatCurrency(getModifiedSalary(salary))}/day</span>
                    </div>
                </div>

                {/* Stat Requirements */}
                {rank && Object.keys(rank).length > 0 && (
                    <div className="mt-4 space-y-2">
                        <p className="text-xs font-semibold uppercase tracking-wider text-gray-400">Requirements</p>
                        <div className="space-y-2">
                            {statReq.map((stat, i) => {
                                const reqValue = rank[stat.type];
                                const meetsReq = checkIfMeetsStatReqs(stat.type, reqValue);
                                return (
                                    <div
                                        key={i}
                                        className={cn(
                                            "flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                                            meetsReq
                                                ? "bg-emerald-900/20 text-emerald-400"
                                                : "bg-red-900/20 text-red-400"
                                        )}
                                    >
                                        <span>{stat.name}</span>
                                        <div className="flex items-center gap-2">
                                            <span>{reqValue}</span>
                                            {meetsReq ? (
                                                <CheckCircle className="size-4" />
                                            ) : (
                                                <XCircle className="size-4" />
                                            )}
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}
