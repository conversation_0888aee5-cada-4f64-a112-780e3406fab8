import { usePersistStore } from "../app/store/stores";
import type { AppRouterClient } from "@/lib/orpc";

export type GameConfig = Awaited<ReturnType<AppRouterClient["user"]["getGameConfig"]>>;

/**
 * Hook to access game configuration with safeguards against destructuring errors
 * @returns {GameConfig} The game configuration object or an empty object if not loaded
 */
function useGameConfig(): GameConfig {
    const { gameConfig } = usePersistStore();

    // Return an empty object instead of null to prevent destructuring errors
    return gameConfig || ({} as GameConfig);
}

export default useGameConfig;
