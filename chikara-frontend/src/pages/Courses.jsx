import greenTickImg from "@/assets/icons/UI/greenTick.png";
import Button from "@/components/Buttons/Button";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useCourseList, useStartCourse } from "@/features/course/api";
import { formatDistanceToNowStrict } from "date-fns";
import toast from "react-hot-toast";
import { formatCurrency } from "@/utils/currencyHelpers";

function classNames(...classes) {
    return classes.filter(Boolean).join(" ");
}

const convertToDays = (milliseconds) => {
    const days = Math.floor(milliseconds / (24 * 60 * 60 * 1000));
    return days;
};

function Courses() {
    const { isLoading, data } = useCourseList();
    const { data: currentUser } = useFetchCurrentUser();

    const startCourseMutation = useStartCourse();

    const handleStartCourse = (courseId) => {
        if (currentUser.activeCourseId && currentUser.activeCourseId > 0) {
            toast.error("You're already on a course!");
            return;
        }

        const selectedCourse = data?.find((course) => course.id === courseId);
        if (selectedCourse && selectedCourse.cost > currentUser.cash) {
            toast.error("You don't have enough cash to start this course!");
            return;
        }

        startCourseMutation.mutate({ courseId });
    };

    const groupedObjects = data?.reduce((acc, obj) => {
        const key = obj.stat;
        if (!acc[key]) {
            acc[key] = [];
        }
        acc[key].push(obj);
        return acc;
    }, {});

    const groupedArray = [];
    for (const key in groupedObjects) {
        groupedArray.push({ stat: key, coursesData: groupedObjects[key] });
    }

    if (isLoading) return <p>Loading...</p>;
    if (currentUser?.level < 8) return null;

    const currentCourse = data.find((course) => course.id === currentUser?.activeCourseId);

    return (
        <div className="px-4 text-shadow sm:px-6 md:mx-auto md:max-w-6xl lg:px-8">
            {currentCourse ? (
                <div className="sm:flex sm:items-center">
                    <div className="mt-3 rounded-lg bg-gray-800 px-5 py-3 md:mx-auto md:px-8">
                        <h1 className="font-normal text-2xl text-gray-900 leading-6 dark:text-slate-400">
                            Current Course: <span className="text-custom-yellow">{currentCourse?.name}</span>
                        </h1>
                        <p className="mt-2 text-base text-gray-700 dark:text-slate-400">
                            Your course will end in{" "}
                            <span className="text-indigo-400">
                                {formatDistanceToNowStrict(Number.parseInt(currentUser?.courseEnds))}
                            </span>
                        </p>
                    </div>
                </div>
            ) : null}
            {groupedArray?.map((courses) => (
                <>
                    <p className="mt-7 mb-2 text-white text-xl uppercase">{capitaliseFirstLetter(courses.stat)}</p>
                    <CoursesTable
                        courses={courses.coursesData}
                        handleStartCourse={handleStartCourse}
                        currentUser={currentUser}
                    />
                </>
            ))}
        </div>
    );
}

const getShortStat = (stat) => {
    switch (stat) {
        case "endurance":
            return "END";
        case "intelligence":
            return "INT";
        case "dexterity":
            return "DEX";
        case "strength":
            return "STR";
        case "vitality":
            return "VIT";
        case "defence":
            return "DEF";

        default:
            return "";
    }
};

const courseStatusIcon = () => {};

export default Courses;

const CoursesTable = ({ courses, handleStartCourse, currentUser }) => {
    return (
        <div className="-mx-4 overflow-hidden bg-white ring-1 ring-gray-300 sm:mx-0 sm:rounded-lg dark:bg-slate-800 dark:ring-gray-600">
            <table className="min-w-full divide-y divide-gray-300 dark:divide-gray-600">
                <thead className="text-gray-900 dark:bg-gray-800 dark:text-gray-200 dark:text-stroke-sm ">
                    <tr>
                        <th
                            scope="col"
                            className="py-3.5 pr-3 pl-4 text-left font-semibold text-sm sm:pl-6 dark:font-normal"
                        >
                            Course
                        </th>
                        <th
                            scope="col"
                            className="hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal"
                        >
                            Stat
                        </th>
                        <th
                            scope="col"
                            className="hidden px-3 py-3.5 text-left font-semibold text-sm lg:table-cell dark:font-normal"
                        >
                            Amount
                        </th>
                        <th
                            scope="col"
                            className="px-2 py-3.5 text-left font-semibold text-sm sm:px-3 lg:table-cell dark:font-normal"
                        >
                            Days
                        </th>
                        <th scope="col" className="px-3 py-3.5 text-left font-semibold text-sm dark:font-normal">
                            Cost
                        </th>
                        <th scope="col" className="relative py-3.5 pr-4 pl-3 sm:pr-6">
                            <span className="sr-only">Select</span>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    {courses?.map((course, courseIdx) => (
                        <tr key={course.id}>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-transparent border-t",
                                    "relative py-4 pl-4 text-sm text-stroke-sm sm:pr-3 sm:pl-6 md:w-80"
                                )}
                            >
                                <div className="font-medium text-custom-yellow text-stroke-md">{course.name}</div>
                                <div className="mt-1 flex flex-col text-gray-500 sm:block lg:hidden dark:text-gray-200">
                                    {/* <span className="text-custom-yellow">{capitaliseFirstLetter(course.stat)}</span> */}
                                    <span className="hidden sm:inline">·</span>
                                    <span className="text-green-500">
                                        +{course.amount}
                                        {""}
                                        <span className="ml-1 text-green-500">{getShortStat(course.stat)}</span>
                                    </span>
                                </div>
                                {courseIdx !== 0 ? (
                                    <div className="-top-px absolute right-0 left-6 h-px bg-gray-200 dark:bg-gray-600" />
                                ) : null}
                            </td>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                    "hidden px-3 py-3.5 text-custom-yellow text-sm text-stroke-sm lg:table-cell"
                                )}
                            >
                                {capitaliseFirstLetter(course.stat)}
                            </td>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                    "hidden px-3 py-3.5 text-green-500 text-sm text-stroke-sm lg:table-cell"
                                )}
                            >
                                +{course.amount}
                            </td>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                    "hidden px-3 py-3.5 text-gray-500 text-sm text-stroke-md lg:table-cell dark:text-gray-200"
                                )}
                            >
                                {convertToDays(course.time)}
                            </td>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                    "px-3 py-3.5 text-gray-500 text-sm text-stroke-md sm:hidden dark:text-gray-200"
                                )}
                            >
                                <div>{convertToDays(course.time)}</div>
                            </td>
                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-gray-200 border-t dark:border-gray-600",
                                    "px-3 py-3.5 text-gray-500 text-sm text-stroke-md dark:text-gray-200"
                                )}
                            >
                                <div>{formatCurrency(course.cost)}</div>
                            </td>

                            <td
                                className={classNames(
                                    courseIdx === 0 ? "" : "border-transparent border-t",
                                    "relative py-3.5 pr-4 pl-3 text-right font-medium text-sm sm:pr-6"
                                )}
                            >
                                {course.completed || course.id === currentUser?.activeCourseId ? (
                                    <div className="md:h-10! text-sm! w-20! md:w-32! flex">
                                        {course.id === currentUser?.activeCourseId ? (
                                            <img
                                                className="m-auto h-7 w-auto rotate-90 animate-pulse"
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/pyBEK4l.png`}
                                                alt=""
                                            />
                                        ) : (
                                            <img className="m-auto h-7 w-auto" src={greenTickImg} alt="" />
                                        )}
                                    </div>
                                ) : (
                                    <Button
                                        variant="primary"
                                        className="md:h-10! text-sm! w-20! md:w-32!"
                                        disabled={currentUser?.activeCourseId && currentUser?.activeCourseId > 0}
                                        onClick={() => handleStartCourse(course.id)}
                                    >
                                        Sign Up<span className="sr-only">, {course.name}</span>
                                    </Button>
                                )}
                                {courseIdx !== 0 ? (
                                    <div className="-top-px absolute right-6 left-0 h-px bg-gray-200 dark:bg-gray-600" />
                                ) : null}
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};
