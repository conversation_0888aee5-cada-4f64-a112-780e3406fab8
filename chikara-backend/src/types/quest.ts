/**
 * Quest Objective types used throughout the application.
 */
export const QuestObjectiveTypes = {
    DEFEAT_NPC: "DEFEAT_NPC",
    DEFEAT_NPC_IN_TURNS: "DEFEAT_NPC_IN_TURNS",
    DEFEAT_NPC_WITH_LOW_DAMAGE: "DEFEAT_NPC_WITH_LOW_DAMAGE",
    DEFEAT_PLAYER: "DEFEAT_PLAYER",
    PVP_POST_BATTLE_CHOICE: "PVP_POST_BATTLE_CHOICE",
    ACQUIRE_ITEM: "ACQUIRE_ITEM",
    CRAFT_ITEM: "CRAFT_ITEM",
    DELIVER_ITEM: "DELIVER_ITEM",
    PLACE_BOUNTY: "PLACE_BOUNTY",
    UNIQUE_OBJECTIVE: "UNIQUE_OBJECTIVE",
    COMPLETE_MISSIONS: "COMPLETE_MISSIONS",
    DONATE_TO_SHRINE: "DONATE_TO_SHRINE",
    DEFEAT_BOSS: "DEFEAT_BOSS",
    VOTE_ON_SUGGESTION: "VOTE_ON_SUGGESTION",
    CHARACTER_ENCOUNTERS: "CHARACTER_ENCOUNTERS",
    WIN_BATTLE: "WIN_BATTLE",
    COLLECT_BOUNTY_REWARD: "COLLECT_BOUNTY_REWARD",
    TRAIN_STATS: "TRAIN_STATS",
    GAMBLING_SLOTS: "GAMBLING_SLOTS",
    DEFEAT_PLAYER_XNAME: "DEFEAT_PLAYER_XNAME",
    DEFEAT_SPECIFIC_PLAYER: "DEFEAT_SPECIFIC_PLAYER",
    // Resource gathering objectives
    GATHER_RESOURCES: "GATHER_RESOURCES",
    // Story quest objectives
    COMPLETE_STORY_EPISODE: "COMPLETE_STORY_EPISODE",
    // MAKE_STORY_CHOICE: "MAKE_STORY_CHOICE",
    // REACH_RELATIONSHIP_LEVEL: "REACH_RELATIONSHIP_LEVEL",
} as const;
export type QuestObjectiveTypes = (typeof QuestObjectiveTypes)[keyof typeof QuestObjectiveTypes];
