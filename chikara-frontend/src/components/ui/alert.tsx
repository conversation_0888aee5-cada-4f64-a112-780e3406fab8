import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";
import { AlertCircle, CheckCircle, Info, XCircle } from "lucide-react";
import * as React from "react";

const alertVariants = cva(
    "relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",
    {
        variants: {
            variant: {
                default: "bg-background text-foreground",
                destructive:
                    "border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",
                success:
                    "border-green-500/50 text-green-700 bg-green-50 dark:bg-green-900/20 dark:border-green-500 dark:text-green-300 [&>svg]:text-green-600 dark:[&>svg]:text-green-400",
                warning:
                    "border-yellow-500/50 text-yellow-700 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-500 dark:text-yellow-300 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",
                info: "border-blue-500/50 text-blue-700 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-500 dark:text-blue-300 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400",
            },
        },
        defaultVariants: {
            variant: "default",
        },
    }
);

const Alert = React.forwardRef<
    HTMLDivElement,
    React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>
>(({ className, variant, ...props }, ref) => (
    <div
        ref={ref}
        role="alert"
        className={cn(alertVariants({ variant }), className)}
        {...props}
    />
));
Alert.displayName = "Alert";

const AlertTitle = React.forwardRef<
    HTMLParagraphElement,
    React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
    <h5
        ref={ref}
        className={cn("mb-1 font-medium leading-none tracking-tight", className)}
        {...props}
    />
));
AlertTitle.displayName = "AlertTitle";

const AlertDescription = React.forwardRef<
    HTMLParagraphElement,
    React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
    <div
        ref={ref}
        className={cn("text-sm [&_p]:leading-relaxed", className)}
        {...props}
    />
));
AlertDescription.displayName = "AlertDescription";

// Icon components for different alert types
const AlertIcon = ({ variant }: { variant?: "default" | "destructive" | "success" | "warning" | "info" }) => {
    switch (variant) {
        case "success":
            return <CheckCircle className="h-4 w-4" />;
        case "destructive":
            return <XCircle className="h-4 w-4" />;
        case "warning":
            return <AlertCircle className="h-4 w-4" />;
        case "info":
            return <Info className="h-4 w-4" />;
        default:
            return <Info className="h-4 w-4" />;
    }
};

export { Alert, AlertDescription, AlertIcon, AlertTitle };
