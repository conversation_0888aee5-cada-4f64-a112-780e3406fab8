import { db } from "../lib/db.js";
import { Prisma, SkillType } from "@prisma/client";
import { Evolution } from "../features/pets/pets.types.js";

export const findAllUsers = async () => {
    return await db.user.findMany();
};

export const findPetsList = async () => {
    return await db.pet.findMany();
};

export const findUserPets = async (userId: number) => {
    return await db.user_pet.findMany({
        where: { userId },
        include: {
            pet: true,
        },
    });
};

export const findRandomUser = async (excludeUserId: number) => {
    const users = await db.user.findMany({
        where: {
            AND: [{ userType: { not: "admin" } }, { id: { not: excludeUserId } }, { level: { gt: 5 } }],
        },
    });
    // Simulate random selection since Prisma doesn't have a direct random function
    return users[Math.floor(Math.random() * users.length)] || null;
};

export const incrementUserStats = async (userId: number, amount: number) => {
    // Update health directly on user table since it's not a skill
    const userUpdate = await db.user.update({
        where: { id: userId },
        data: {
            currentHealth: { increment: amount },
            health: { increment: amount },
        },
    });

    // Update combat stats through skill system
    const combatStats: SkillType[] = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];

    for (const skillType of combatStats) {
        await db.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: { increment: amount },
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: 1 + amount,
                experience: 0,
                talentPoints: 0,
            },
        });
    }

    return userUpdate;
};

export const decrementUserStats = async (userId: number, amount: number) => {
    // Update health directly on user table since it's not a skill
    const userUpdate = await db.user.update({
        where: { id: userId },
        data: {
            currentHealth: { decrement: amount },
            health: { decrement: amount },
        },
    });

    // Update combat stats through skill system
    const combatStats: SkillType[] = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];

    for (const skillType of combatStats) {
        // Get current skill level to ensure we don't go below 1
        const currentSkill = await db.user_skill.findUnique({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
        });

        const currentLevel = currentSkill?.level || 1;
        const newLevel = Math.max(1, currentLevel - amount); // Don't go below level 1

        await db.user_skill.upsert({
            where: {
                userId_skillType: {
                    userId,
                    skillType,
                },
            },
            update: {
                level: newLevel,
                updatedAt: new Date(),
            },
            create: {
                userId,
                skillType,
                level: 1,
                experience: 0,
                talentPoints: 0,
            },
        });
    }

    return userUpdate;
};

export const createUserItem = async (userId: number, itemId: number, count: number) => {
    return await db.user_item.create({
        data: {
            userId,
            itemId,
            count,
        },
    });
};

export const deleteQuestProgress = async (userId: number) => {
    return await db.quest_progress.deleteMany({
        where: { userId },
    });
};

export const findNonAdminUsers = async () => {
    return await db.user.findMany({
        where: {
            userType: {
                not: "admin",
            },
        },
    });
};

export const findUserQuestItem = async (userId: number, itemId: number) => {
    return await db.user_item.findFirst({
        where: {
            userId,
            itemId,
            item: {
                itemType: "quest",
            },
        },
        select: {
            id: true,
            count: true,
            itemId: true,
            item: {
                select: {
                    itemType: true,
                },
            },
        },
    });
};

export const findAllQuests = async () => {
    return await db.quest.findMany();
};

export const createQuestProgress = async (
    userId: number,
    questId: number,
    status: "complete" | "in_progress" | "ready_to_complete"
) => {
    return await db.quest_progress.create({
        data: {
            userId,
            questId,
            questStatus: status,
        },
    });
};

export const createTraderRep = async (userId: number, shopId: number, level: number) => {
    return await db.trader_rep.create({
        data: {
            userId,
            shopId,
            reputationLevel: level,
        },
    });
};

export const findRecentChatMessages = async (limit: number) => {
    return await db.chat_message.findMany({
        orderBy: {
            createdAt: "desc",
        },
        take: Math.min(limit),
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    createdAt: true,
                    userType: true,
                    level: true,
                },
            },
        },
    });
};

export const removeEffects = async (userId: number) => {
    return await db.user_status_effect.deleteMany({
        where: { userId },
    });
};

export const findUserEggs = async (userId: number) => {
    return await db.user_pet.findMany({
        where: { userId, evolution: { path: "$.current", equals: "egg" } },
    });
};

export const updatePetProgress = async (userId: number, petId: number, evolution: Evolution) => {
    return await db.user_pet.update({
        where: { id: petId },
        data: { evolution },
    });
};

export const setAllPetsHappiness = async (userId: number) => {
    return await db.user_pet.updateMany({
        where: { userId },
        data: { happiness: 100 },
    });
};

export const addXpToAllPets = async (userId: number, xpAmount: number) => {
    // Get all user's pets
    const userPets = await db.user_pet.findMany({
        where: { userId },
    });

    // Update each pet's XP
    const updatePromises = userPets.map((pet) => {
        return db.user_pet.update({
            where: { id: pet.id },
            data: { xp: { increment: xpAmount } },
        });
    });

    return await Promise.all(updatePromises);
};

export const deleteExploreNodes = async (userId: number) => {
    return await db.explore_player_node.deleteMany({
        where: { userId },
    });
};
