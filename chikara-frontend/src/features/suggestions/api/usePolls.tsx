import { api, type QueryOptions } from "@/helpers/api";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Hook to get available polls for user
 */
export const useGetAvailablePolls = (options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.getAvailablePolls.queryOptions({
            ...options,
        })
    );
};

/**
 * Hook to get poll results
 */
export const useGetPollResults = (pollId: number, options: QueryOptions = {}) => {
    return useQuery(
        api.suggestions.getPollResults.queryOptions({
            input: { pollId },
            ...options,
        })
    );
};

/**
 * Hook to submit poll response
 */
export const useSubmitPollResponse = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.suggestions.submitPollResponse.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getAvailablePolls.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.suggestions.getPollResults.key(),
                });
                toast.success("Poll response submitted successfully!");
            },
            onError: (error) => {
                console.error("Submit poll response error:", error);
                toast.error(error.message || "Failed to submit poll response");
            },
        })
    );
};
