import { Callout } from "@/components/TestComponents/Callout";
import { UsersTable } from "@/components/UsersTable";
import { useState } from "react";
import toast from "react-hot-toast";
import Button from "@/components/Buttons/Button";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGameConfig from "@/hooks/useGameConfig";
import { useActiveBounties } from "@/features/bounty/api/useActiveBounties";
import { usePlaceBounty } from "@/features/bounty/api/usePlaceBounty";
import { formatCurrency, getCurrencySymbol } from "@/utils/currencyHelpers";

function BountyBoard() {
    const { data, isLoading, error } = useActiveBounties();

    if (error) return <div>An error has occurred: {error.message}</div>;

    return (
        <div className="mb-8 pb-4 md:mx-auto md:mb-0 md:max-w-6xl md:pb-0">
            <Callout
                className="mt-3 mb-4"
                title="Here you will find students that have been placed on the bounty list."
            >
                <p className="text-gray-300!">
                    You must defeat the student in a PvP battle and choose to &apos;Cripple&apos; them in order to claim
                    the bounty reward!
                </p>
            </Callout>
            <PlaceBounty />
            <UsersTable data={data} isLoading={isLoading} type="bounties" />
        </div>
    );
}

export default BountyBoard;

const PlaceBounty = () => {
    const [studentID, setStudentID] = useState<string>("");
    const [amount, setAmount] = useState<string>("");
    const [reason, setReason] = useState<string>("");
    const { MIN_BOUNTY, BOUNTY_FEE } = useGameConfig();
    const { data: currentUser } = useFetchCurrentUser();

    const placeBountyMutation = usePlaceBounty(() => {
        setStudentID("");
        setAmount("");
        setReason("");
    });

    const handlePlaceBounty = (): void => {
        if (!studentID.trim()) {
            toast.error("Enter a valid Student ID");
            return;
        }
        if (!amount.trim()) {
            toast.error("Enter a reward amount");
            return;
        }
        if (!reason.trim()) {
            toast.error("Enter a bounty reason");
            return;
        }

        const amountNum = Number.parseInt(amount);
        const studentIDNum = Number.parseInt(studentID);

        if (isNaN(amountNum) || amountNum < MIN_BOUNTY) {
            toast.error(`Bounty rewards must be at least ${formatCurrency(MIN_BOUNTY)}`);
            return;
        }
        if (isNaN(studentIDNum) || studentIDNum <= 0) {
            toast.error("Enter a valid Student ID");
            return;
        }

        const bountyFee = BOUNTY_FEE + 1;
        if (amountNum * bountyFee > (currentUser?.cash || 0)) {
            toast.error(`You need more cash to place this bounty!`);
            return;
        }

        placeBountyMutation.mutate({
            amount: amountNum,
            targetId: studentIDNum,
            reason: reason,
        });
    };

    return (
        <div className="mb-4 grid h-36 grid-cols-6 grid-rows-2 gap-x-4 gap-y-2 px-4 md:flex md:h-16 md:gap-5 md:px-0">
            <div className="col-span-3 md:w-[10%]">
                <label
                    className="mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200"
                    htmlFor="studentid"
                >
                    Student ID<span className="text-red-500"> *</span>
                </label>
                <div className="mt-1 flex rounded-md shadow-xs">
                    <span className="inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200">
                        #
                    </span>
                    <input
                        type="number"
                        name="studentid"
                        min={1}
                        id="studentid"
                        value={studentID}
                        className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                        placeholder="1"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setStudentID(e.target.value);
                        }}
                    />
                </div>
            </div>

            <div className="col-span-3 md:w-1/6">
                <label
                    className="mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200"
                    htmlFor="amount"
                >
                    Reward<span className="text-red-500"> *</span>{" "}
                    <span className="text-gray-400">({BOUNTY_FEE * 100}% fee)</span>
                </label>
                <div className="mt-1 flex rounded-md shadow-xs">
                    <span className="inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200">
                        {getCurrencySymbol("yen")}
                    </span>
                    <input
                        type="number"
                        name="amount"
                        id="amount"
                        value={amount}
                        min={MIN_BOUNTY}
                        className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                        placeholder={MIN_BOUNTY.toString()}
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setAmount(e.target.value);
                        }}
                    />
                </div>
            </div>

            <div className="col-span-4 md:w-2/6">
                <label
                    className="mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200"
                    htmlFor="reason"
                >
                    Reason<span className="text-red-500"> *</span>
                </label>
                <div className="mt-1 flex rounded-md shadow-xs">
                    <input
                        type="text"
                        name="reason"
                        id="reason"
                        value={reason}
                        className="block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                        placeholder="No Reason"
                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                            setReason(e.target.value);
                        }}
                    />
                </div>
            </div>
            <Button
                isLoading={placeBountyMutation.isPending}
                variant="flat"
                className="col-span-2 mt-auto"
                textSize="text-xs md:text-sm"
                onClick={() => handlePlaceBounty()}
            >
                Place Bounty
            </Button>
        </div>
    );
};
