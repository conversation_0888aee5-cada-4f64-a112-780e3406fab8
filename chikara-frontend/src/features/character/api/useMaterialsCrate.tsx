import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Custom hook to use materials crate
 */
export const useMaterialsCrate = () => {
    const queryClient = useQueryClient();

    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        api.specialItems.useMaterialsCrate.mutationOptions({
            onSuccess: () => {
                setTimeout(() => {
                    queryClient.invalidateQueries({
                        queryKey: api.user.getInventory.key(),
                    });
                    queryClient.invalidateQueries({
                        queryKey: api.user.getCurrentUserInfo.key(),
                    });
                }, 30);

                toast.success("You redeemed 10 Raw Materials for your gang!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useMaterialsCrate;
