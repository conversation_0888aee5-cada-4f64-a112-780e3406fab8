import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import Exam from "./Exam";

export default function ExamsTab() {
    const [examOpen, setExamOpen] = useState(null);
    const [examResults, setExamResults] = useState(null);

    if (examOpen)
        return (
            <Exam examId={examOpen} setOpen={setExamOpen} examResults={examResults} setExamResults={setExamResults} />
        );

    return (
        <div className="font-display! mx-auto flex size-full flex-col items-center gap-4 rounded-md border border-gray-700/50 bg-black/20 p-5 font-body text-stroke-sm">
            <ExamButton
                setExamOpen={setExamOpen}
                setExamResults={setExamResults}
                examId={1}
                examType="General Knowledge"
            />
            <ExamButton
                setExamOpen={setExamOpen}
                setExamResults={setExamResults}
                examId={2}
                examType="Combat & Adventure Mode"
            />
            <ExamButton setExamOpen={setExamOpen} setExamResults={setExamResults} examId={3} examType="Godzilla Lore" />
        </div>
    );
}

const ExamButton = ({ setExamOpen, setExamResults, examId, examType }) => {
    return null; // TODO: Exams need to be re-implemented

    // const { data: examResults } = useQuery({
    //     queryKey: api.user.getExamResults + examId,
    // });

    // let complete = false;
    // if (examResults && examResults.complete) complete = true;

    // const handleResultsOpen = (id) => {
    //     setExamResults(examResults.results);
    //     setExamOpen(id);
    // };

    // return (
    //     <button
    //         type="button"
    //         className={cn(
    //             complete ? "border-4 border-green-800" : "border-gray-700",
    //             "group relative mx-auto flex h-24 w-full cursor-pointer select-none flex-col items-center justify-center overflow-hidden rounded-lg border bg-background hover:ring-2 active:scale-[97%] md:h-32 md:w-3/4"
    //         )}
    //         onClick={() => (complete ? handleResultsOpen(examId) : setExamOpen(examId))}
    //     >
    //         <div className="flex flex-row-reverse gap-5 pr-14 md:flex-row md:gap-6 md:pl-20">
    //             <div className="my-auto">
    //                 {" "}
    //                 <p className="z-10 whitespace-pre-wrap text-center text-3xl text-black text-stroke-md tracking-tighter dark:text-custom-yellow">
    //                     EXAM {examId}
    //                 </p>
    //                 <p className="z-10 font-medium text-base text-blue-500 transition-transform group-hover:scale-[1.2] group-hover:text-sky-400 dark:text-stroke-s-sm">
    //                     {examType}
    //                 </p>
    //             </div>
    //             <div className="flex flex-col">
    //                 <img
    //                     className="z-10 my-auto ml-2 3xl:h-24 h-16 w-auto max-w-16 rounded-full leading-none brightness-95 md:h-20 md:max-w-24 lg:ml-0"
    //                     alt=""
    //                     src={
    //                         complete
    //                             ? `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/YlE5sXv.png`
    //                             : `${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/CRXUUFA.png`
    //                     }
    //                 />
    //                 {complete && (
    //                     <p className="-mt-1 z-10 font-bold text-green-500 text-lg transition-transform group-hover:scale-[1.2] group-hover:text-green-400 dark:text-stroke-s-sm">
    //                         {examResults?.results?.correctPoints}/{examResults?.results?.totalPoints}
    //                     </p>
    //                 )}
    //             </div>
    //         </div>
    //         <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
    //     </button>
    // );
};

const DisabledButton = ({ examId, unlockDate }) => {
    return (
        <button
            disabled
            type="button"
            className="relative mx-auto flex h-24 w-full select-none items-center justify-center gap-6 overflow-hidden rounded-lg border border-gray-700 bg-background grayscale md:h-32 md:w-3/4"
        >
            <div>
                <p className="z-10 whitespace-pre-wrap text-center font-medium text-4xl text-black tracking-tighter dark:text-gray-400 dark:text-stroke-s-sm">
                    EXAM {examId}
                </p>
                <p>Unlocks {unlockDate}</p>
            </div>
            <img
                className="z-10 my-auto 3xl:h-24 h-16 w-auto rounded-full md:h-20"
                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/CRXUUFA.png`}
                alt=""
            />

            <div className="pointer-events-none absolute inset-0 h-full bg-[radial-gradient(circle_at_50%_120%,rgba(120,119,198,0.3),rgba(255,255,255,0))]" />
        </button>
    );
};
