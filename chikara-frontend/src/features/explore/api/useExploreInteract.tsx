import { useMutation, useQueryClient, type QueryClient } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { api } from "@/helpers/api";
import type { User } from "@/types/user";
import { useNormalStore } from "../../../app/store/stores";
import type { ExploreCharacterDialogue } from "../types/explore.types";
import type { StoryEpisodeData } from "@/features/story/types/story";

interface UseExploreInteractOptions {
    onSelectedNodeChange?: (_nodeId: number | null) => void;
}

interface EncounterData {
    dialogue: ExploreCharacterDialogue;
    healed?: boolean;
    goodOutcome?: boolean;
}

const optimisticCashUpdate = (encounterData: EncounterData, queryClient: QueryClient) => {
    const rewardAmount = encounterData.dialogue.rewards;
    if (typeof rewardAmount === "number" && rewardAmount > 0) {
        queryClient.setQueryData(api.user.getCurrentUserInfo.key(), (old: User) => {
            if (!old) return old;

            let updatedCash = old.cash;
            if (encounterData.dialogue.mugged) {
                updatedCash -= rewardAmount;
                updatedCash = Math.max(0, updatedCash);
            } else {
                updatedCash += rewardAmount;
            }

            return {
                ...old,
                cash: updatedCash,
            };
        });
    }
};

const useExploreInteract = (options?: UseExploreInteractOptions) => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const { setJustJailed } = useNormalStore();

    return useMutation(
        api.explore.interactWithNode.mutationOptions({
            onSuccess: (response) => {
                if (!response.success) return;
                const { data } = response;

                if (data && data.action === "character_encounter") {
                    const encounterData = data as EncounterData;

                    queryClient.invalidateQueries({
                        queryKey: api.explore.getMapByLocation.key(),
                    });

                    // Update cash to prevent refetch
                    optimisticCashUpdate(encounterData, queryClient);

                    if (encounterData.dialogue.jailed) {
                        setJustJailed(true);
                    }
                } else if (data && data.action === "battle") {
                    // For battles, navigate to fight page (node is auto-completed in backend)
                    queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                    navigate(`/fight`);
                } else if (data && data.action === "story") {
                    // For story nodes, navigate to story episode player
                    const storyData = data as {
                        redirectToStoryPlayer?: boolean;
                        episodeData?: StoryEpisodeData;
                    };

                    if (storyData.redirectToStoryPlayer && storyData.episodeData?.id) {
                        queryClient.invalidateQueries({
                            queryKey: api.explore.getMapByLocation.key(),
                        });
                    }
                } else if (data && data.action) {
                    // For scavenging, invalidate map data to refresh the current node with choices
                    queryClient.invalidateQueries({
                        queryKey: api.explore.getMapByLocation.key(),
                    });
                }

                // Reset selected node
                if (options?.onSelectedNodeChange) {
                    options.onSelectedNodeChange(null);
                }
            },
            onError: (error) => {
                console.error("Node interaction error:", error);

                // Reset selected node
                if (options?.onSelectedNodeChange) {
                    options.onSelectedNodeChange(null);
                }
            },
        })
    );
};

export default useExploreInteract;
