import { UserModel, db } from "../lib/db.js";
import { QuestObjectiveTypes } from "../types/quest.js";
import { Prisma, QuestProgressStatus } from "@prisma/client";

export const getUserQuestProgress = async (userId: number, questId: number) => {
    return await db.quest_progress.findFirst({
        where: { userId, questId },
    });
};

export const getCreatureById = async (targetId: number) => {
    return await db.creature.findUnique({
        where: { id: targetId },
        select: {
            id: true,
            name: true,
            boss: true,
            image: true,
        },
    });
};

export const getCompleteQuestList = async () => {
    return await db.quest.findMany({
        include: {
            quest_objective: true,
        },
    });
};

export const getAvailableQuestsForUser = async (userId: number, userLevel: number) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: false,
            NOT: {
                quest_progress: {
                    some: {
                        userId: userId,
                        questStatus: {
                            in: [
                                QuestProgressStatus.in_progress,
                                QuestProgressStatus.complete,
                                QuestProgressStatus.ready_to_complete,
                            ],
                        },
                    },
                },
            },
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};

export const getActiveQuestsForUser = async (userId: number, userLevel: number) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: false,
            quest_progress: {
                some: {
                    userId: userId,
                    questStatus: {
                        in: [QuestProgressStatus.in_progress, QuestProgressStatus.ready_to_complete],
                    },
                },
            },
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};

export const getCompletedQuestsForUser = async (userId: number) => {
    return await db.quest.findMany({
        where: {
            quest_progress: {
                some: {
                    userId: userId,
                    questStatus: QuestProgressStatus.complete,
                },
            },
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};

export const getUserQuestProgressList = async (userId: number, activeOnly: boolean) => {
    const filter: { userId: number; questStatus?: QuestProgressStatus } = { userId };
    if (activeOnly) {
        filter.questStatus = QuestProgressStatus.in_progress;
    }

    return await db.quest_progress.findMany({
        where: filter,
        orderBy: { updatedAt: "desc" },
        include: {
            quest: {
                include: {
                    quest_objective: true,
                },
            },
        },
    });
};

export const getQuestsWithUserProgress = async (user: { id: UserModel["id"]; level: UserModel["level"] }) => {
    const { id: userId } = user;

    return await db.quest.findMany({
        where: {
            levelReq: {
                lte: user.level,
            },
            disabled: false,
        },
        include: {
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
        },
    });
};

export const getFullQuestDetails = async (userId: number, userLevel: number, onlyStoryQuests = false) => {
    return await db.quest.findMany({
        where: {
            levelReq: { lte: userLevel },
            disabled: { not: true },
            isStoryQuest: onlyStoryQuests ? true : undefined,
            // Don't include quests that have been completed
            NOT: {
                quest_progress: {
                    some: {
                        userId: userId,
                        questStatus: QuestProgressStatus.complete,
                    },
                },
            },
            // Only include quests with no prerequisite OR where the prerequisite is completed
            OR: [
                { requiredQuestId: null },
                {
                    requiredQuestId: {
                        not: null,
                    },
                    quest: {
                        quest_progress: {
                            some: {
                                userId: userId,
                                questStatus: QuestProgressStatus.complete,
                            },
                        },
                    },
                },
            ],
        },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                    quest_objective_progress: {
                        where: {
                            userId: userId,
                        },
                    },
                },
            },
            quest_progress: {
                where: {
                    userId: userId,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
        orderBy: {
            levelReq: "asc",
        },
    });
};

export const getQuestById = async (questId: number) => {
    return await db.quest.findUnique({
        where: { id: questId },
        include: {
            quest_reward: {
                include: {
                    item: true,
                },
            },
            quest_objective: true,
        },
    });
};

export const createQuestProgress = async (userId: number, questId: number, questStatus: QuestProgressStatus) => {
    return await db.quest_progress.create({
        data: {
            user: { connect: { id: userId } },
            quest: { connect: { id: questId } },
            questStatus,
        },
    });
};

export const getQuestByName = async (name: string) => {
    return await db.quest.findFirst({
        where: { name },
        include: {
            quest_objective: true,
        },
    });
};

export const updateQuestProgress = async (id: number, updateData: Prisma.quest_progressUpdateInput) => {
    return await db.quest_progress.update({
        where: { id },
        data: updateData,
    });
};

export const updateQuestObjectiveProgress = async (
    id: number,
    updateData: Prisma.quest_objective_progressUpdateInput
) => {
    return await db.quest_objective_progress.update({
        where: { id },
        data: updateData,
    });
};

export const createQuestObjectiveProgress = async (
    userId: number,
    questObjectiveId: number,
    count: number,
    status: QuestProgressStatus
) => {
    return await db.quest_objective_progress.create({
        data: {
            userId,
            questObjectiveId,
            count,
            status,
        },
    });
};

export const findQuestProgressWithQuest = async (userId: number, activeOnly = false) => {
    const filter: { userId: number; questStatus?: QuestProgressStatus } = { userId };
    if (activeOnly) {
        filter.questStatus = QuestProgressStatus.in_progress;
    }

    return await db.quest_progress.findMany({
        where: filter,
        orderBy: { updatedAt: "desc" },
        include: {
            quest: {
                include: {
                    quest_objective: { include: { quest_objective_progress: true } },
                },
            },
        },
    });
};

export const findFullUserQuestProgress = async (userId: number) => {
    const questProgress = await db.quest_progress.findMany({
        where: { userId },
        orderBy: { updatedAt: "desc" },
    });

    const allObjectiveProgress = await db.quest_objective_progress.findMany({
        where: { userId },
        include: { quest_objective: { select: { questId: true } } },
    });

    const fullProgress = questProgress.map((progress) => {
        const questId = progress.questId;
        const questObjectiveProgress = allObjectiveProgress.filter((p) => p.quest_objective.questId === questId);
        return {
            ...progress,
            quest_objective_progress: questObjectiveProgress,
        };
    });

    return fullProgress;
};

export const findQuestObjectivesByType = async (questType: QuestObjectiveTypes) => {
    return await db.quest_objective.findMany({
        where: {
            objectiveType: questType,
        },
        include: {
            quest: true,
        },
    });
};

export const findQuestObjectiveProgressForUser = async (userId: number, questObjectiveId: number) => {
    return await db.quest_objective_progress.findFirst({
        where: {
            userId,
            questObjectiveId,
        },
    });
};

export const findUserQuestObjectiveProgress = async (userId: number, where: Prisma.quest_objectiveWhereInput) => {
    return await db.quest_objective_progress.findMany({
        where: {
            userId,
            status: QuestProgressStatus.in_progress,
            quest_objective: where,
        },
        include: {
            quest_objective: true,
        },
    });
};

export const findRequiredUserQuestObjectiveProgressForQuest = async (userId: number, questId: number) => {
    return await db.quest_objective_progress.findMany({
        where: {
            userId,
            quest_objective: { questId: questId, isRequired: true },
        },
        include: {
            quest_objective: true,
        },
    });
};

export const findTraderRep = async (userId: number, shopId: number) => {
    return await db.trader_rep.findFirst({
        where: { userId, shopId },
    });
};

export const createTraderRep = async (userId: number, shopId: number, rep: number) => {
    return await db.trader_rep.create({
        data: {
            userId,
            shopId,
            reputationLevel: rep,
        },
    });
};

export const saveTraderRep = async (traderRep: { id: number; reputationLevel?: number }) => {
    return await db.trader_rep.update({
        where: { id: traderRep.id },
        data: { reputationLevel: traderRep.reputationLevel },
    });
};

/**
 * Gets a quest objective progress by its ID
 * @param id The ID of the quest objective progress
 * @returns The quest objective progress or null if not found
 */
export const getQuestObjectiveProgressById = async (id: number) => {
    return await db.quest_objective_progress.findUnique({
        where: { id },
    });
};

// ===============================================
// Admin Functions
// ===============================================

/**
 * Get all quests with full details for admin display
 */
export const getAllQuestsWithDetails = async () => {
    return await db.quest.findMany({
        include: {
            quest_objective: {
                include: {
                    creature: true,
                    item: true,
                },
            },
            quest_reward: {
                include: {
                    item: true,
                },
            },
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
            shop: true,
            quest: true, // required quest
        },
        orderBy: [{ chapterId: "asc" }, { orderInChapter: "asc" }, { id: "asc" }],
    });
};

/**
 * Create a new quest
 */
export const createQuest = async (questData: Prisma.questCreateInput) => {
    return await db.quest.create({
        data: questData,
        include: {
            quest_objective: true,
            quest_reward: true,
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};

/**
 * Update a quest
 */
export const updateQuest = async (questId: number, questData: Prisma.questUpdateInput) => {
    return await db.quest.update({
        where: { id: questId },
        data: questData,
        include: {
            quest_objective: true,
            quest_reward: true,
            story_chapter: {
                include: {
                    story_season: true,
                },
            },
        },
    });
};

/**
 * Delete a quest
 */
export const deleteQuest = async (questId: number) => {
    return await db.quest.delete({
        where: { id: questId },
    });
};

/**
 * Reorder quests within a chapter
 * Uses a two-step approach to avoid unique constraint violations:
 * 1. First, set all orderInChapter values to negative numbers (temporary)
 * 2. Then, set them to the correct positive values (1-based indexing)
 */
export const reorderQuestsInChapter = async (chapterId: number, questIds: number[]) => {
    if (!questIds || questIds.length === 0) {
        throw new Error("Quest IDs array cannot be empty");
    }

    return await db.$transaction(async (tx) => {
        // Verify all quests belong to the specified chapter
        const questsInChapter = await tx.quest.findMany({
            where: {
                id: { in: questIds },
                chapterId: chapterId,
            },
            select: { id: true },
        });

        if (questsInChapter.length !== questIds.length) {
            throw new Error("Some quests do not belong to the specified chapter");
        }

        // Step 1: Set all quest orders to negative values to avoid constraint conflicts
        for (const [i, questId] of questIds.entries()) {
            await tx.quest.update({
                where: {
                    id: questId,
                    chapterId: chapterId, // Additional safety check
                },
                data: { orderInChapter: -(i + 1) }, // Use negative values temporarily
            });
        }

        // Step 2: Set the correct positive order values (1-based indexing)
        for (const [i, questId] of questIds.entries()) {
            await tx.quest.update({
                where: {
                    id: questId,
                    chapterId: chapterId, // Additional safety check
                },
                data: { orderInChapter: i + 1 }, // Use 1-based indexing
            });
        }
    });
};

/**
 * Get all quest objectives with details for admin display
 */
export const getAllObjectivesWithDetails = async () => {
    return await db.quest_objective.findMany({
        include: {
            quest: {
                include: {
                    story_chapter: {
                        include: {
                            story_season: true,
                        },
                    },
                },
            },
            creature: true,
            item: true,
        },
        orderBy: [{ questId: "asc" }],
    });
};

/**
 * Create a new quest objective
 */
export const createObjective = async (objectiveData: Prisma.quest_objectiveCreateInput) => {
    return await db.quest_objective.create({
        data: objectiveData,
        include: {
            quest: true,
            creature: true,
            item: true,
        },
    });
};

/**
 * Update a quest objective
 */
export const updateObjective = async (objectiveId: number, objectiveData: Prisma.quest_objectiveUpdateInput) => {
    return await db.quest_objective.update({
        where: { id: objectiveId },
        data: objectiveData,
        include: {
            quest: true,
            creature: true,
            item: true,
        },
    });
};

/**
 * Delete a quest objective
 */
export const deleteObjective = async (objectiveId: number) => {
    return await db.quest_objective.delete({
        where: { id: objectiveId },
    });
};

/**
 * Gets a quest objective by its ID with related item if applicable
 * @param id The ID of the quest objective
 * @returns The quest objective or null if not found
 */
export const getQuestObjectiveById = async (id: number) => {
    return await db.quest_objective.findUnique({
        where: { id },
        include: {
            item: true,
        },
    });
};

/**
 * Gets the next story quest in a chapter
 * @param chapterId The ID of the chapter
 * @param currentOrder The order of the current quest
 * @returns The next quest in the chapter or null if not found
 */
export const getNextStoryQuestInChapter = async (chapterId: number, currentOrder: number) => {
    return await db.quest.findFirst({
        where: {
            chapterId,
            orderInChapter: currentOrder + 1,
            isStoryQuest: true,
        },
        include: {
            quest_objective: true,
        },
    });
};
