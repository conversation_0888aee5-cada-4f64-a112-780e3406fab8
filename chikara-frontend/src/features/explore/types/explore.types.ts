import type { Item } from "@/types/item";

export type NodeStatus = "available" | "current" | "completed" | "locked";

export type ExploreNodeLocation = "shibuya" | "shinjuku" | "bunkyo" | "chiyoda" | "minato";

export type TravelMethod = "walk" | "bus";

export type ExploreNodeType =
    | "SHOP"
    | "HOUSING"
    | "BATTLE"
    | "CHARACTER_ENCOUNTER"
    | "ACTION"
    | "CONDITION"
    | "CHOICE"
    | "STORY"
    | "MINING_NODE"
    | "SCAVENGE_NODE"
    | "FORAGING_NODE";

export interface ExploreCharacterDialogue {
    character: string;
    location: string;
    line: string;
    isItemDrop: boolean;
    rewards: number | Item;
    mugged: boolean;
    crateReward?: Item;
    hospitalised?: boolean;
    jailed?: boolean;
}

export interface EncounterMetadata {
    dialogue: ExploreCharacterDialogue;
    healed?: boolean;
    goodOutcome?: boolean;
}

export interface EncounterData {
    dialogue: ExploreCharacterDialogue;
    healed?: boolean;
    nodeId: number;
}

// Scavenging types
export interface ScavengeLocation {
    location: string;
    description: string;
    choices: {
        [key: string]: {
            action: string;
            success: string;
            failureInjury: string;
            injury: string;
            failureJail: string;
        };
    };
}

export interface ScavengeResult {
    success: boolean;
    choice: string;
    itemReward?: Item;
    itemQuantity?: number;
    jailed?: boolean;
    jailDuration?: number;
    injury?: {
        id: number;
        name: string;
        tier: string;
        [key: string]: any;
    };
    scavengeData?: ScavengeLocation;
    message?: string;
}

export interface ScavengeChoices {
    choices: string[];
    scavengeValidUntil: number;
    scavengeData: ScavengeLocation;
}

// Travel-related types
export interface TravelStatus {
    isTravel: boolean;
    travelingTo?: ExploreNodeLocation;
    travelStartTime?: Date;
    travelEndTime?: Date;
    travelMethod?: TravelMethod;
    remainingTime?: number; // in milliseconds
}

export interface TravelRequest {
    location: ExploreNodeLocation;
    method: TravelMethod;
}

export interface TravelResponse {
    newLocation?: ExploreNodeLocation;
    travelingTo?: ExploreNodeLocation;
    newCash: number;
    cost: number;
    travelTime: number; // in minutes
    method: TravelMethod;
    travelStartTime?: string;
    travelEndTime?: string;
}

export interface TravelMethodOption {
    method: TravelMethod;
    name: string;
    description: string;
    cost: number;
    time: number; // in minutes
    icon: string;
}

export interface MapNodeData {
    id: number;
    nodeType: ExploreNodeType;
    title: string;
    description: string;
    position: { x: number; y: number };
    metadata?: Record<string, unknown>;
    status: NodeStatus;
    isStatic: boolean;
    expiresAt?: Date;
    location: ExploreNodeLocation;
    shopId: number | null;
}

export interface MapData {
    nodes: MapNodeData[];
    travelStatus: TravelStatus;
}
