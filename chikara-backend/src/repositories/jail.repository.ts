import { db } from "../lib/db.js";

export const findJailedUsers = async () => {
    return await db.user.findMany({
        where: {
            jailedUntil: { not: null },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            level: true,
            jailedUntil: true,
            jailReason: true,
        },
    });
};

export const updateUserJailStatus = async (userId: number, jailEndTime?: bigint, reason?: string) => {
    return await db.user.update({
        where: { id: userId },
        data: { jailedUntil: jailEndTime || null, jailReason: reason || null },
    });
};
