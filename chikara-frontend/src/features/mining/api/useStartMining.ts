import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

const useStartMining = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.startMining.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.mining.getMiningSession.key() });
            },
        })
    );
};

export default useStartMining;
