import { FriendRequestResponse } from "@/features/social/types/social";
import { api } from "@/helpers/api";
import { UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface RespondToFriendRequestParams {
    requestId: number;
    accept: boolean;
}

const useRespondToFriendRequest = (
    options: Partial<UseMutationOptions<FriendRequestResponse, Error, RespondToFriendRequestParams>> = {}
) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.respondToFriendRequest.mutationOptions({
            onSuccess: (data) => {
                if (data.accepted) {
                    toast.success("Friend request accepted!");
                    // Invalidate friends list to refresh with new friend
                    queryClient.invalidateQueries({ queryKey: api.social.getFriends.key() });
                } else {
                    toast.success("Friend request declined");
                }
                // Invalidate friend requests list in either case
                queryClient.invalidateQueries({ queryKey: api.social.getFriendRequests.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to respond to friend request");
            },
            ...options,
        })
    );
};

export default useRespondToFriendRequest;
