import { useEffect, useRef } from "react";

export default function useOuterClick(callback: (event: MouseEvent) => void) {
    const callbackRef = useRef<(event: MouseEvent) => void | null>(null); // initialize mutable ref, which stores callback
    const innerRef = useRef<HTMLElement | null>(null); // returned to client, who marks "border" element

    // update cb on each render, so second useEffect has access to current value
    useEffect(() => {
        callbackRef.current = callback;
    });

    useEffect(() => {
        function handleClick(e: MouseEvent) {
            if (innerRef.current && callbackRef.current && e.target && !innerRef.current.contains(e.target as Node)) {
                callbackRef.current(e);
            }
        }
        document.addEventListener("click", handleClick);
        return () => document.removeEventListener("click", handleClick);
    }, []); // no dependencies -> stable click listener

    return innerRef; // convenience for client (doesn't need to init ref himself)
}
