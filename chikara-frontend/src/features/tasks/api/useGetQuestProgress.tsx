import { api, type QueryOptions } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

interface UseGetQuestProgressOptions extends QueryOptions {
    activeOnly?: boolean;
}

const useGetQuestProgress = (options: UseGetQuestProgressOptions = {}) => {
    const { activeOnly = false, ...queryOptions } = options;

    return useQuery(
        api.quests.getProgress.queryOptions({
            input: { activeOnly },
            ...queryOptions,
        })
    );
};

export default useGetQuestProgress;
