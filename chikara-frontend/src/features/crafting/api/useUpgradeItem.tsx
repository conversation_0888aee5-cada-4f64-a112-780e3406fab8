import { api } from "@/helpers/api";
import type { InventoryItem } from "@/types/item";
import { type UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Request parameters for upgrading an item
 */
export interface UpgradeItemParams {
    upgradeCores: {
        small?: { count: number; id: number };
        medium?: { count: number; id: number };
        large?: { count: number; id: number };
        giant?: { count: number; id: number };
    };
    itemId: number;
    resetModal: (_newItem: InventoryItem | null) => void;
}

/**
 * Response type for the upgrade item mutation
 */
type UpgradeItemResponse = {
    message: string;
    newItemInstance?: InventoryItem;
};

/**
 * Custom hook to upgrade an item
 * @param options - Optional mutation configuration
 * @param onSuccessCallback - Optional callback function to execute on successful upgrade
 */
const useUpgradeItem = (
    options: Partial<UseMutationOptions<UpgradeItemResponse, Error, UpgradeItemParams>> = {},
    onSuccessCallback?: () => void
) => {
    const queryClient = useQueryClient();

    return useMutation<UpgradeItemResponse, Error, UpgradeItemParams>(
        api.items.upgradeItem.mutationOptions({
            onSuccess: (res, params) => {
                if (res.message === "Upgrade failed") {
                    toast.error(res.message);
                } else {
                    toast.success(res.message);
                }

                // Invalidate inventory and upgrade items queries
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                queryClient.invalidateQueries({ queryKey: api.items.getUpgradeItems.key() });

                const newItem = res.newItemInstance || null;
                params.resetModal(newItem);

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error("Upgrade item error:", errorMessage);
                toast.error(errorMessage);
            },
            ...options,
        })
    );
};

export default useUpgradeItem;
