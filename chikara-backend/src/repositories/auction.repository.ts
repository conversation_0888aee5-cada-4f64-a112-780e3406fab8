import { TransactionClient, db } from "../lib/db.js";
import type { AuctionItemStatus } from "@prisma/client";
import * as UserRepository from "./user.repository.js";

export const findAuctionItems = async (date: Date) => {
    return await db.auction_item.findMany({
        where: {
            status: "in_progress",
            endsAt: { gt: date },
        },
        include: {
            item: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
        orderBy: {
            endsAt: "asc",
        },
    });
};

export const createAuctionItem = async (data: {
    itemId: number;
    sellerId: number;
    quantity: number;
    deposit: number;
    buyoutPrice: number;
    endsAt: Date;
    bankFunds: boolean;
}) => {
    return await db.auction_item.create({
        data: {
            ...data,
            status: "in_progress",
        },
    });
};

export const findActiveAuctionItem = async (auctionItemId: number) => {
    return await db.auction_item.findFirst({
        where: {
            id: auctionItemId,
            status: "in_progress",
        },
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                },
            },
            user: {
                select: {
                    id: true,
                    username: true,
                },
            },
        },
    });
};

export const updateAuctionItemStatus = async (auctionItemId: number, status: AuctionItemStatus) => {
    return await db.auction_item.update({
        where: { id: auctionItemId },
        data: { status },
    });
};

export const updateAuctionItemQuantity = async (auctionItemId: number, quantity: number) => {
    return await db.auction_item.update({
        where: { id: auctionItemId },
        data: { quantity },
    });
};

export const updateBuyerCashTransaction = async (prisma: TransactionClient, buyerId: number, newCash: number) => {
    return await prisma.user.update({
        where: { id: buyerId },
        data: { cash: newCash },
    });
};

export const findSellerTransaction = async (prisma: TransactionClient, sellerId: number) => {
    return await prisma.user.findUnique({
        where: { id: sellerId },
    });
};

export const updateSellerBalanceTransaction = async (
    prisma: TransactionClient,
    sellerId: number,
    bankFunds: boolean,
    bankBalance: number,
    cash: number
) => {
    return await prisma.user.update({
        where: { id: sellerId },
        data: bankFunds ? { bank_balance: bankBalance } : { cash },
    });
};

export const updateAuctionItemTransaction = async (
    prisma: TransactionClient,
    auctionItemId: number,
    newQuantity: number,
    status: AuctionItemStatus
) => {
    return await prisma.auction_item.update({
        where: { id: auctionItemId },
        data: {
            quantity: newQuantity,
            status,
        },
        include: {
            item: true,
            user: true,
        },
    });
};

export const updateUserBankBalanceTransaction = async (
    prisma: TransactionClient,
    userId: number,
    bankBalance: number
) => {
    return await prisma.user.update({
        where: { id: userId },
        data: { bank_balance: bankBalance },
    });
};

export const updateUserCashTransaction = async (prisma: TransactionClient, userId: number, cash: number) => {
    return await prisma.user.update({
        where: { id: userId },
        data: { cash },
    });
};

export const findAuctionItemWithDetails = async (auctionItemId: number) => {
    return await db.auction_item.findFirst({
        where: {
            id: auctionItemId,
            status: "in_progress",
        },
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                },
            },
            user: {
                select: {
                    id: true,
                    username: true,
                },
            },
        },
    });
};

export const executeAuctionTransaction = async (
    prisma: TransactionClient,
    buyerId: number,
    buyerNewCash: number,
    sellerId: number,
    sellerNewCash: number,
    auctionItemId: number,
    newQuantity: number,
    status: AuctionItemStatus,
    bankFunds = false,
    sellerBankBalance?: number
) => {
    // Update buyer's cash
    await updateUserCashTransaction(prisma, buyerId, buyerNewCash);

    // Update seller's balance
    if (bankFunds && sellerBankBalance !== undefined) {
        await updateUserBankBalanceTransaction(prisma, sellerId, sellerBankBalance);
    } else {
        await updateUserCashTransaction(prisma, sellerId, sellerNewCash);
    }

    // Update auction item
    return updateAuctionItemTransaction(prisma, auctionItemId, newQuantity, status);
};
