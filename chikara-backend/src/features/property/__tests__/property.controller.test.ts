import { describe, it, expect, vi, beforeEach } from "vitest";
import * as PropertyController from "../property.controller.js";
import * as propertyRepository from "../../../repositories/property.repository.js";

// Mock the repository
vi.mock("../../../repositories/property.repository.js");

describe("Property Controller", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("getUserProperties", () => {
        it("should return user properties with details", async () => {
            const mockUserId = 1;
            const mockUserProperties = [
                {
                    id: 1,
                    userId: 1,
                    propertyId: 1,
                    isPrimary: true,
                    purchaseDate: new Date("2023-01-01"),
                    lastUpkeepPaid: new Date("2023-01-01"),
                    furniture: [],
                    customization: {},
                    createdAt: new Date("2023-01-01"),
                    updatedAt: new Date("2023-01-01"),
                    property: {
                        id: 1,
                        name: "Capsule Hotel Pod",
                        propertyType: "housing",
                        cost: 15000,
                        upkeep: 200,
                        slots: 1,
                        buffs: {
                            energyRegen: 1.02,
                            healthRegen: 1.02,
                        },
                        description: "A minimal sleeping pod in a capsule hotel.",
                        image: "https://i.imgur.com/IiVmNPF.png",
                        createdAt: new Date("2023-01-01"),
                        updatedAt: new Date("2023-01-01"),
                    },
                },
                {
                    id: 2,
                    userId: 1,
                    propertyId: 2,
                    isPrimary: false,
                    purchaseDate: new Date("2023-01-02"),
                    lastUpkeepPaid: new Date("2023-01-02"),
                    furniture: [],
                    customization: {},
                    createdAt: new Date("2023-01-02"),
                    updatedAt: new Date("2023-01-02"),
                    property: {
                        id: 2,
                        name: "Manga Cafe Premium Room",
                        propertyType: "housing",
                        cost: 25000,
                        upkeep: 300,
                        slots: 1,
                        buffs: {
                            energyRegen: 1.02,
                            healthRegen: 1.02,
                            gamingBonus: 1.05,
                        },
                        description: "A private room in a manga cafe.",
                        image: null,
                        createdAt: new Date("2023-01-01"),
                        updatedAt: new Date("2023-01-01"),
                    },
                },
            ];

            vi.mocked(propertyRepository.findUserPropertiesWithDetails).mockResolvedValue(mockUserProperties);

            const result = await PropertyController.getUserProperties(mockUserId);

            expect(result).toEqual({ data: mockUserProperties });
            expect(propertyRepository.findUserPropertiesWithDetails).toHaveBeenCalledWith(mockUserId);
        });

        it("should handle empty user properties", async () => {
            const mockUserId = 1;
            const mockUserProperties: any[] = [];

            vi.mocked(propertyRepository.findUserPropertiesWithDetails).mockResolvedValue(mockUserProperties);

            const result = await PropertyController.getUserProperties(mockUserId);

            expect(result).toEqual({ data: [] });
            expect(propertyRepository.findUserPropertiesWithDetails).toHaveBeenCalledWith(mockUserId);
        });

        it("should handle repository errors", async () => {
            const mockUserId = 1;
            const mockError = new Error("Database error");

            vi.mocked(propertyRepository.findUserPropertiesWithDetails).mockRejectedValue(mockError);

            await expect(PropertyController.getUserProperties(mockUserId)).rejects.toThrow("Database error");
            expect(propertyRepository.findUserPropertiesWithDetails).toHaveBeenCalledWith(mockUserId);
        });
    });
});
