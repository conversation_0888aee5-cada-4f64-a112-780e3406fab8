import { db } from "../lib/db.js";
import { Prisma, QuestProgressStatus } from "@prisma/client";
import {
    StorySeasonData,
    StoryChapterData,
    StoryEpisodeData,
    StoryEpisodeContent,
    StoryEpisodeChoices,
} from "../features/story/story.types.js";
import { parseJsonArray } from "../utils/jsonHelper.js";

/**
 * Transform raw episode data to StoryEpisodeData format
 */
const transformEpisodeData = (episode: Prisma.story_episodeGetPayload<object>): StoryEpisodeData => {
    if (!episode.objectiveId) {
        throw new Error(`Episode ${episode.id} is missing required objectiveId`);
    }

    return {
        ...episode,
        objectiveId: episode.objectiveId,
        exploreLocation: episode.exploreLocation || "shibuya", // Default to shibuya for existing episodes
        content: episode.content as StoryEpisodeContent,
        choices: episode.choices as StoryEpisodeChoices | null,
        // rewards: episode.rewards as StoryEpisodeRewards | null,
        // requiredStoryFlags: parseJsonArray<string>(episode.requiredStoryFlags), // V2 Feature
    };
};

// ===============================================
// Story Content Queries
// ===============================================

/**
 * Find all story seasons
 */
export const findAllSeasons = async (): Promise<StorySeasonData[]> => {
    const seasons = await db.story_season.findMany({
        orderBy: { startDate: "asc" },
    });
    return seasons;
};

/**
 * Find a specific season by ID
 */
export const findSeasonById = async (seasonId: number): Promise<StorySeasonData | null> => {
    const season = await db.story_season.findUnique({
        where: { id: seasonId },
    });
    if (!season) return null;
    return season;
};

/**
 * Find all chapters across all seasons
 */
export const findAllChapters = async (): Promise<StoryChapterData[]> => {
    const chapters = await db.story_chapter.findMany({
        orderBy: [{ seasonId: "asc" }, { order: "asc" }],
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    }));
};

/**
 * Find all episodes across all chapters
 */
export const findAllEpisodes = async (): Promise<StoryEpisodeData[]> => {
    const episodes = await db.story_episode.findMany({
        orderBy: [{ quest_objective: { quest: { chapterId: "asc" } } }],
    });
    return episodes.map((episode) => transformEpisodeData(episode));
};

/**
 * Find all chapters for a season
 */
export const findChaptersBySeasonId = async (seasonId: number): Promise<StoryChapterData[]> => {
    const chapters = await db.story_chapter.findMany({
        where: { seasonId },
        orderBy: { order: "asc" },
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    }));
};

/**
 * Find all chapters for multiple seasons
 */
export const findChaptersBySeasonIds = async (seasonIds: number[]): Promise<StoryChapterData[]> => {
    if (seasonIds.length === 0) {
        return [];
    }

    const chapters = await db.story_chapter.findMany({
        where: {
            seasonId: {
                in: seasonIds,
            },
        },
        orderBy: [{ seasonId: "asc" }, { order: "asc" }],
    });
    return chapters.map((chapter) => ({
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    }));
};

/**
 * Find a specific chapter by ID
 */
export const findChapterById = async (chapterId: number): Promise<StoryChapterData | null> => {
    const chapter = await db.story_chapter.findUnique({
        where: { id: chapterId },
    });
    if (!chapter) return null;
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    };
};

/**
 * Find all episodes for a chapter
 * Episodes are linked to quest objectives, so we need to:
 * 1. Get all quests for the chapter
 * 2. Get all objectives for those quests
 * 3. Get all episodes for those objectives
 */
export const findEpisodesByChapterId = async (chapterId: number): Promise<StoryEpisodeData[]> => {
    const episodes = await db.story_episode.findMany({
        where: {
            quest_objective: {
                quest: {
                    chapterId: chapterId,
                },
            },
        },
        include: {
            quest_objective: {
                include: {
                    quest: true,
                },
            },
        },
        orderBy: {
            quest_objective: {
                quest: {
                    orderInChapter: "asc",
                },
            },
        },
    });
    return episodes.map((episode) => transformEpisodeData(episode));
};

/**
 * Find all episodes for multiple chapters in a single query
 * Episodes are linked to quest objectives, so we need to:
 * 1. Get all quests for the chapters
 * 2. Get all objectives for those quests
 * 3. Get all episodes for those objectives
 */
export const findEpisodesByChapterIds = async (chapterIds: number[]): Promise<StoryEpisodeData[]> => {
    if (chapterIds.length === 0) {
        return [];
    }

    const episodes = await db.story_episode.findMany({
        where: {
            quest_objective: {
                quest: {
                    chapterId: {
                        in: chapterIds,
                    },
                },
            },
        },
        include: {
            quest_objective: {
                include: {
                    quest: true,
                },
            },
        },
        orderBy: [
            {
                quest_objective: {
                    quest: {
                        chapterId: "asc",
                    },
                },
            },
            {
                quest_objective: {
                    quest: {
                        orderInChapter: "asc",
                    },
                },
            },
        ],
    });

    return episodes.map((episode) => transformEpisodeData(episode));
};

/**
 * Find a specific episode by ID
 */
export const findEpisodeById = async (episodeId: number): Promise<StoryEpisodeData | null> => {
    const episode = await db.story_episode.findUnique({
        where: { id: episodeId },
    });
    if (!episode) return null;
    return transformEpisodeData(episode);
};

// ===============================================
// User and Quest Integration
// ===============================================

/**
 * Get user's completed quest IDs
 */
export const getUserCompletedQuests = async (userId: number): Promise<number[]> => {
    const completedQuests = await db.quest_progress.findMany({
        where: {
            userId,
            questStatus: QuestProgressStatus.complete,
        },
        select: {
            questId: true,
        },
    });

    return completedQuests.map((q) => q.questId).filter((id): id is number => id !== null);
};

// ===============================================
// Admin Operations
// ===============================================

/**
 * Create a new season
 */
export const createSeason = async (data: Prisma.story_seasonCreateInput): Promise<StorySeasonData> => {
    const season = await db.story_season.create({
        data: data,
    });
    return season;
};

/**
 * Update a season
 */
export const updateSeason = async (
    seasonId: number,
    data: Prisma.story_seasonUpdateInput
): Promise<StorySeasonData> => {
    const season = await db.story_season.update({
        where: { id: seasonId },
        data: data,
    });
    return season;
};

/**
 * Delete a season
 */
export const deleteSeason = async (seasonId: number): Promise<void> => {
    await db.story_season.delete({
        where: { id: seasonId },
    });
};

/**
 * Create a new chapter
 */
export const createChapter = async (data: Prisma.story_chapterCreateInput): Promise<StoryChapterData> => {
    const createData = {
        ...data,
        requiredChapterIds: data.requiredChapterIds === null ? Prisma.JsonNull : data.requiredChapterIds,
    };

    const chapter = await db.story_chapter.create({
        data: createData,
    });
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    };
};

/**
 * Update a chapter
 */
export const updateChapter = async (
    chapterId: number,
    data: Prisma.story_chapterUpdateInput
): Promise<StoryChapterData> => {
    const updateData = {
        ...data,
        requiredChapterIds: data.requiredChapterIds === null ? Prisma.JsonNull : data.requiredChapterIds,
    };

    const chapter = await db.story_chapter.update({
        where: { id: chapterId },
        data: updateData,
    });
    return {
        ...chapter,
        requiredChapterIds: parseJsonArray<number>(chapter.requiredChapterIds),
    };
};

/**
 * Delete a chapter
 */
export const deleteChapter = async (chapterId: number): Promise<void> => {
    await db.story_chapter.delete({
        where: { id: chapterId },
    });
};

/**
 * Create a new episode
 */
export const createEpisode = async (data: Prisma.story_episodeCreateInput): Promise<StoryEpisodeData> => {
    const createData = {
        ...data,
        choices: data.choices === null ? Prisma.JsonNull : data.choices,
        // rewards: data.rewards === null ? Prisma.JsonNull : data.rewards,
        // requiredStoryFlags: data.requiredStoryFlags === null ? Prisma.JsonNull : data.requiredStoryFlags, // V2 Feature
    };

    const episode = await db.story_episode.create({
        data: createData,
    });
    return transformEpisodeData(episode);
};

/**
 * Update an episode
 */
export const updateEpisode = async (
    episodeId: number,
    data: Prisma.story_episodeUpdateInput
): Promise<StoryEpisodeData> => {
    const updateData = {
        ...data,
        choices: data.choices === null ? Prisma.JsonNull : data.choices,
        // rewards: data.rewards === null ? Prisma.JsonNull : data.rewards,
        // requiredStoryFlags: data.requiredStoryFlags === null ? Prisma.JsonNull : data.requiredStoryFlags, // V2 Feature
    };

    const episode = await db.story_episode.update({
        where: { id: episodeId },
        data: updateData,
    });
    return transformEpisodeData(episode);
};

/**
 * Delete an episode
 */
export const deleteEpisode = async (episodeId: number): Promise<void> => {
    await db.story_episode.delete({
        where: { id: episodeId },
    });
};

// ===============================================
// Story Node Management
// ===============================================

/**
 * Find an episode by its linked quest objective ID
 */
export const findEpisodeByObjectiveId = async (objectiveId: number): Promise<StoryEpisodeData | null> => {
    const episode = await db.story_episode.findFirst({
        where: { objectiveId },
    });
    if (!episode) return null;
    return transformEpisodeData(episode);
};
