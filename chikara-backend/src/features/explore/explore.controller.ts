import type { ExploreNodeLocation, ExploreNodeStatus, TravelMethod } from "@prisma/client";
import { logPlayerAction } from "../../lib/actionLogger.js";
import { handleError, handleInternalError, logger } from "../../utils/log.js";
import { PLAYER_NODE_CONFIG, TRAVEL_COSTS, TRAVEL_TIMES } from "./explore.constants.js";
import * as ExploreHelpers from "./explore.helpers.js";
import * as ExploreRepository from "../../repositories/explore.repository.js";
import type { ReturnMapNode, TravelInitiationResponse, TravelStatus } from "./explore.types.js";
import type { ExtUserModel } from "../../lib/db.js";

/**
 * Get user's explore map for a specific location with travel status
 */
export const getExploreMapByLocation = async (user: ExtUserModel) => {
    const { currentMapLocation: location, id: userId } = user;
    if (!location) {
        return handleInternalError("No current location found");
    }

    // Get travel status and auto-complete travel if needed first
    const completionResult = await ExploreRepository.autoCompleteTravelIfNeeded(user);

    // If travel was auto-completed, log the action
    if (completionResult.autoCompleted && completionResult.completedDestination) {
        await logPlayerAction(
            "EXPLORE_TRAVEL_COMPLETED",
            {
                newLocation: completionResult.completedDestination,
                autoCompleted: true,
            },
            userId
        );

        logger.info(`User ${userId} travel auto-completed to ${completionResult.completedDestination}`);
    }

    // Determine travel status from existing user object or updated state after auto-completion
    let travelStatus: TravelStatus;
    if (completionResult.autoCompleted) {
        // If travel was auto-completed, user is no longer traveling
        travelStatus = {
            isTravel: false,
            travelingTo: undefined,
            travelStartTime: undefined,
            travelEndTime: undefined,
            travelMethod: undefined,
            remainingTime: undefined,
        };
    } else {
        // Use travel status from existing user object
        const now = new Date();
        const isTravel = !!(user.travelStartTime && user.travelEndTime && now < user.travelEndTime);

        travelStatus = {
            isTravel,
            travelingTo: isTravel ? user.currentMapLocation || undefined : undefined,
            travelStartTime: user.travelStartTime || undefined,
            travelEndTime: user.travelEndTime || undefined,
            travelMethod: user.travelMethod || undefined,
            remainingTime: isTravel && user.travelEndTime ? user.travelEndTime.getTime() - now.getTime() : undefined,
        };
    }

    // If user is currently traveling, return empty nodes array with travel status
    if (travelStatus.isTravel) {
        logger.info(`User ${userId} is currently traveling, skipping map node generation`);
        return {
            data: {
                nodes: [],
                travelStatus: travelStatus,
            },
        };
    }

    // Clean up expired nodes first
    const expiredCount = await ExploreRepository.cleanupExpiredPlayerNodes(userId);
    if (expiredCount > 0) {
        logger.info(`Cleaned up ${expiredCount} expired nodes for user ${userId}`);
    }

    // Generate new player nodes if needed for this location
    await ExploreHelpers.generatePlayerNodesIfNeeded(userId, location);

    // Fetch static nodes for the location
    const staticNodes = await ExploreRepository.getStaticNodesByLocation(location);

    // Fetch active player nodes for the location
    const playerNodes = await ExploreRepository.getActivePlayerNodesByLocation(userId, location);

    // Filter out disabled node types from both static and player nodes
    const enabledStaticNodes = staticNodes.filter((node) => !ExploreHelpers.isNodeTypeDisabled(node.nodeType));
    const enabledPlayerNodes = playerNodes.filter((node) => !ExploreHelpers.isNodeTypeDisabled(node.nodeType));

    // Combine and format nodes for frontend
    // Use negative IDs for static nodes to prevent ID collisions with player nodes
    const combinedNodes: ReturnMapNode[] = [
        ...enabledStaticNodes.map((node) => ({
            id: -node.id, // Make static node IDs negative to prevent collisions
            nodeType: node.nodeType,
            title: node.title,
            description: node.description,
            position: node.position,
            metadata: node.metadata,
            status: "available" as ExploreNodeStatus,
            isStatic: true,
            location: node.location,
            shopId: node.shopId,
            expiresAt: null, // Static nodes don't expire
        })),
        ...enabledPlayerNodes.map((node) => ({
            id: node.id,
            nodeType: node.nodeType,
            title: node.title,
            description: node.description,
            position: node.position,
            metadata: node.metadata,
            status: node.status,
            isStatic: false,
            location: node.location,
            shopId: null,
            expiresAt: node.expiresAt,
        })),
    ];

    logger.info(
        `Returning ${combinedNodes.length} nodes for user ${userId} in ${location} (${enabledStaticNodes.length}/${staticNodes.length} static, ${enabledPlayerNodes.length}/${playerNodes.length} player)`
    );

    return {
        data: {
            nodes: combinedNodes,
            travelStatus: travelStatus,
        },
    };
};

/**
 * Handle interaction with a specific node
 */
export const interactWithNode = async (userId: number, nodeId: number, isStatic: boolean) => {
    logger.info(`User ${userId} interacting with node ${nodeId} (static: ${isStatic})`);

    let actualNodeId = nodeId;

    // Convert negative ID back to positive for static nodes
    if (isStatic && nodeId < 0) {
        actualNodeId = -nodeId;
    }

    // Return error if user is trying to interact with a static node
    if (isStatic) {
        logger.warn(`User ${userId} attempted to interact with static node ${nodeId}`);
        return handleError("Can't interact with static node", 404);
    }

    // Lock the node for interaction
    const nodeData = await ExploreRepository.lockPlayerNodeForInteraction(actualNodeId, userId);

    if (!nodeData) {
        logger.warn(`Node ${actualNodeId} not found, expired, or already in use for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }

    logger.info(`Locked player node ${actualNodeId} for interaction by user ${userId}`);

    // Handle the interaction based on node type
    const interactionResult = await ExploreHelpers.handleNodeInteraction(
        nodeData.nodeType,
        nodeData.metadata,
        userId,
        actualNodeId,
        isStatic,
        nodeData.location
    );

    // If interaction failed, unlock the node so it can be tried again
    if (interactionResult.success) {
        // Auto-complete BATTLE nodes since they're always consumed when interacted with
        if (nodeData.nodeType === "BATTLE") {
            const completed = await ExploreRepository.completePlayerNodeInteraction(
                actualNodeId,
                userId,
                PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES
            );

            if (completed) {
                logger.info(`Auto-completed BATTLE node ${actualNodeId} for user ${userId}`);
            } else {
                logger.warn(`Failed to auto-complete BATTLE node ${actualNodeId} for user ${userId}`);
            }
        }
    } else {
        const unlocked = await ExploreRepository.unlockPlayerNodeOnFailure(actualNodeId, userId);

        if (unlocked) {
            logger.info(`Unlocked player node ${actualNodeId} after failed interaction for user ${userId}`);
        } else {
            logger.warn(`Failed to unlock player node ${actualNodeId} after failed interaction for user ${userId}`);
        }
    }

    // Log the action
    await logPlayerAction(
        "EXPLORE_NODE_INTERACTION",
        {
            nodeId: actualNodeId, // Log the actual database ID
            nodeType: nodeData.nodeType,
            isStatic,
            interactionResult: interactionResult.data?.action || "unknown",
        },
        userId
    );

    return {
        data: {
            ...interactionResult,
            nodeId,
            isStatic,
        },
    };
};

/**
 * Complete a node interaction and set it to completed status
 */
export const completeNode = async (userId: number, nodeId: number) => {
    logger.info(`User ${userId} completing node ${nodeId}`);

    // Complete the node interaction
    const completed = await ExploreRepository.completePlayerNodeInteraction(
        nodeId,
        userId,
        PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES
    );

    if (!completed) {
        logger.warn(`Failed to complete node ${nodeId} for user ${userId} - node not found or not in 'current' status`);
        return handleError("Node not found or not ready for completion", 404);
    }

    logger.info(`Successfully completed player node ${nodeId} for user ${userId}`);

    // Log the action
    await logPlayerAction(
        "EXPLORE_NODE_COMPLETION",
        {
            nodeId,
        },
        userId
    );

    return {
        data: {
            nodeId,
            status: "completed",
            message: "Node completed successfully",
        },
    };
};

/**
 * Handle scavenging choice for a specific node
 */
export const makeScavengeChoice = async (userId: number, nodeId: number, choiceIndex: number) => {
    logger.info(`User ${userId} making scavenge choice ${choiceIndex} for node ${nodeId}`);

    // Get the node to validate it's a scavenging node and get current choices
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);

    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }

    if (nodeData.nodeType !== "SCAVENGE_NODE") {
        logger.warn(`User ${userId} tried to make scavenge choice on non-scavenge node ${nodeId}`);
        return handleError("This node is not a scavenging node", 400);
    }

    if (nodeData.status !== "current") {
        logger.warn(`User ${userId} tried to make scavenge choice on node ${nodeId} with status ${nodeData.status}`);
        return handleError("This node is not ready for scavenging choices", 400);
    }

    // Get current choices from metadata
    const currentChoices = nodeData.metadata?.choices as string[] | undefined;
    if (!currentChoices || currentChoices.length < 2) {
        logger.warn(`Node ${nodeId} does not have valid scavenge choices in metadata`);
        return handleError("No scavenging choices available", 400);
    }

    // Import here to avoid circular dependencies
    const { processScavengeChoice } = await import("./nodes/scavenging.node.js");

    // Process the scavenging choice
    const scavengeResult = await processScavengeChoice(userId, nodeId, nodeData.location, choiceIndex, currentChoices);

    if (!scavengeResult.success) {
        logger.warn(`Scavenge choice failed for user ${userId} on node ${nodeId}: ${scavengeResult.message}`);
        return handleError(scavengeResult.message, 400);
    }

    // Complete the node after successful scavenging
    const completed = await ExploreRepository.completePlayerNodeInteraction(
        nodeId,
        userId,
        PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES
    );

    if (completed) {
        logger.info(`Completed scavenging node ${nodeId} for user ${userId}`);
    } else {
        logger.warn(`Failed to complete scavenging node ${nodeId} for user ${userId}`);
    }

    // Log the action
    await logPlayerAction(
        "EXPLORE_SCAVENGE_CHOICE",
        {
            nodeId,
            choiceIndex,
            choice: currentChoices[choiceIndex - 1],
            success: scavengeResult.data?.success || false,
            itemReward: scavengeResult.data?.itemReward?.name || null,
        },
        userId
    );

    logger.info(`User ${userId} successfully made scavenge choice ${choiceIndex} for node ${nodeId}`);

    return {
        data: {
            nodeId,
            choiceIndex,
            ...scavengeResult.data,
        },
    };
};

/**
 * Initiate travel to a new location with specified method
 */
export const changeMapLocation = async (userId: number, newLocation: ExploreNodeLocation, method: TravelMethod) => {
    logger.info(`User ${userId} attempting to travel to ${newLocation} via ${method}`);

    // Validate travel parameters using the helper function and throw an error if invalid
    ExploreHelpers.validateTravelParameters(method, newLocation);

    // Get the cost and travel time for the method and location
    // These are now guaranteed to be valid due to the validation above
    const cost = TRAVEL_COSTS[method][newLocation];
    const travelTimeMinutes = TRAVEL_TIMES[method][newLocation];

    // Initiate travel
    const updatedUser = await ExploreRepository.initiateTravel(userId, newLocation, method, cost, travelTimeMinutes);

    // Log the action
    await logPlayerAction(
        "EXPLORE_TRAVEL_INITIATED",
        {
            destination: newLocation,
            method,
            cost,
            travelTimeMinutes,
            newCash: updatedUser.cash,
            travelEndTime: updatedUser.travelEndTime,
        },
        userId
    );

    logger.info(
        `User ${userId} successfully initiated travel to ${newLocation} via ${method} for ${cost} cash, arriving in ${travelTimeMinutes} minutes`
    );

    return {
        data: {
            travelingTo: updatedUser.currentMapLocation || newLocation,
            newCash: updatedUser.cash,
            cost,
            travelTime: travelTimeMinutes,
            method,
            travelStartTime: updatedUser.travelStartTime || new Date(),
            travelEndTime: updatedUser.travelEndTime || new Date(Date.now() + travelTimeMinutes * 60 * 1000),
        } as TravelInitiationResponse,
    };
};

/**
 * Process mining operation for a specific node
 */
export const processMiningOperation = async (userId: number, nodeId: number) => {
    logger.info(`User ${userId} processing mining operation for node ${nodeId}`);

    // Get the node to validate it's a mining node and get current operation details
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);

    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }

    if (nodeData.nodeType !== "MINING_NODE") {
        logger.warn(`User ${userId} tried to process mining operation on non-mining node ${nodeId}`);
        return handleError("This node is not a mining node", 400);
    }

    if (nodeData.status !== "current") {
        logger.warn(
            `User ${userId} tried to process mining operation on node ${nodeId} with status ${nodeData.status}`
        );
        return handleError("This node is not ready for mining", 400);
    }

    // Get mining operation details from metadata
    const miningType = nodeData.metadata?.miningType as string;
    const difficulty = nodeData.metadata?.difficulty as "easy" | "medium" | "hard";

    if (!miningType || !difficulty) {
        logger.warn(`Node ${nodeId} does not have valid mining operation details in metadata`);
        return handleError("No mining operation details available", 400);
    }

    // Import here to avoid circular dependencies
    const { processMiningOperation: processOperation } = await import("./nodes/mining.node.js");

    // Process the mining operation
    const miningResult = await processOperation(userId, nodeId, nodeData.location, miningType, difficulty);

    if (!miningResult.success) {
        logger.warn(`Mining operation failed for user ${userId} on node ${nodeId}: ${miningResult.message}`);
        return handleError(miningResult.message, 400);
    }

    // Complete the node after successful mining
    const completed = await ExploreRepository.completePlayerNodeInteraction(
        nodeId,
        userId,
        PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES
    );

    if (completed) {
        logger.info(`Completed mining node ${nodeId} for user ${userId}`);
    } else {
        logger.warn(`Failed to complete mining node ${nodeId} for user ${userId}`);
    }

    // Log the action
    await logPlayerAction(
        "EXPLORE_MINING_OPERATION",
        {
            nodeId,
            miningType,
            difficulty,
            success: miningResult.data?.success || false,
            itemReward: miningResult.data?.itemReward?.name || null,
            injury: miningResult.data?.injury?.name || null,
        },
        userId
    );

    logger.info(`User ${userId} successfully processed mining operation for node ${nodeId}`);

    return {
        data: {
            nodeId,
            ...miningResult.data,
        },
    };
};

/**
 * Process foraging operation for a specific node
 */
export const processForagingOperation = async (userId: number, nodeId: number) => {
    logger.info(`User ${userId} processing foraging operation for node ${nodeId}`);

    // Get the node to validate it's a foraging node and get current operation details
    const nodeData = await ExploreRepository.getPlayerNodeById(nodeId, userId);

    if (!nodeData) {
        logger.warn(`Node ${nodeId} not found for user ${userId}`);
        return handleError("Node not found or has expired", 404);
    }

    if (nodeData.nodeType !== "FORAGING_NODE") {
        logger.warn(`User ${userId} tried to process foraging operation on non-foraging node ${nodeId}`);
        return handleError("This node is not a foraging node", 400);
    }

    if (nodeData.status !== "current") {
        logger.warn(
            `User ${userId} tried to process foraging operation on node ${nodeId} with status ${nodeData.status}`
        );
        return handleError("This node is not ready for foraging", 400);
    }

    // Get foraging operation details from metadata
    const foragingType = (nodeData.metadata?.foragingType as string) || "herbs";
    const difficulty = (nodeData.metadata?.difficulty as "easy" | "medium" | "hard") || "easy";

    // Import here to avoid circular dependencies
    const { processForaging: processOperation } = await import("./nodes/foraging.node.js");

    // Process the foraging operation
    const foragingResult = await processOperation(userId, nodeId, nodeData.location, foragingType, difficulty);

    if (!foragingResult.success) {
        logger.warn(`Foraging operation failed for user ${userId} on node ${nodeId}: ${foragingResult.message}`);
        return handleError(foragingResult.message, 400);
    }

    // Complete the node after successful foraging
    const completed = await ExploreRepository.completePlayerNodeInteraction(
        nodeId,
        userId,
        PLAYER_NODE_CONFIG.COMPLETION_EXPIRATION_MINUTES
    );

    if (completed) {
        logger.info(`Completed foraging node ${nodeId} for user ${userId}`);
    } else {
        logger.warn(`Failed to complete foraging node ${nodeId} for user ${userId}`);
    }

    // Log the action
    await logPlayerAction(
        "EXPLORE_FORAGING_OPERATION",
        {
            nodeId,
            foragingType,
            difficulty,
            success: foragingResult.data?.success || false,
            itemReward: foragingResult.data?.itemReward?.name || null,
            injury: foragingResult.data?.injury?.name || null,
            experienceGained: foragingResult.data?.experienceGained || null,
        },
        userId
    );

    logger.info(`User ${userId} successfully processed foraging operation for node ${nodeId}`);

    return {
        data: {
            nodeId,
            ...foragingResult.data,
        },
    };
};
