import { db } from "../lib/db.js";

export const findAllPolls = async () => {
    return await db.poll_response.findMany({
        orderBy: {
            createdAt: "desc",
        },
    });
};

export const findPollResponsesByUserId = async (userId: number) => {
    return await db.poll_response.findMany({
        where: {
            userId,
        },
        select: {
            pollId: true,
        },
    });
};

export const findAvailablePolls = async (completedPollIds: number[]) => {
    return await db.poll.findMany({
        where: {
            id: {
                notIn: completedPollIds.length > 0 ? completedPollIds : undefined,
            },
            ended: false,
        },
        orderBy: {
            createdAt: "desc",
        },
    });
};

export const findPollById = async (pollId: number) => {
    return await db.poll.findUnique({
        where: { id: pollId },
    });
};

export const findPollResponses = async (pollId: number) => {
    return await db.poll_response.findMany({
        where: { pollId },
        select: {
            id: true,
            answer: true,
            createdAt: true,
            updatedAt: true,
            pollId: true,
        },
    });
};

export const findPreviousPollResponse = async (userId: number, pollId: number) => {
    return await db.poll_response.findFirst({
        where: {
            AND: [{ userId }, { pollId }],
        },
    });
};

export const createPollResponse = async (userId: number, pollId: number, answer: Record<string, unknown>) => {
    return await db.poll_response.create({
        data: {
            answer,
            userId,
            pollId,
        },
    });
};
