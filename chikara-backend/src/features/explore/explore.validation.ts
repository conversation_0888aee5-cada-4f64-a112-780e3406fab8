import { ExploreNodeLocation, TravelMethod } from "@prisma/client";
import { z } from "zod";

export const InteractWithNodeSchema = z.object({
    nodeId: z.number().int().positive(),
    isStatic: z.boolean(),
});

export const CompleteNodeSchema = z.object({
    nodeId: z.number().int().positive(),
});

export const ChangeMapSchema = z.object({
    location: z.nativeEnum(ExploreNodeLocation),
    method: z.nativeEnum(TravelMethod),
});

export const ScavengeChoiceSchema = z.object({
    nodeId: z.number().int().positive(),
    choiceIndex: z.number().int().min(1).max(2),
});

export const ProcessMiningSchema = z.object({
    nodeId: z.number().int().positive(),
});

export const ProcessForagingSchema = z.object({
    nodeId: z.number().int().positive(),
});

const exploreSchema = {
    interactWithNode: InteractWithNodeSchema,
    completeNode: CompleteNodeSchema,
    changeMap: ChangeMapSchema,
    scavengeChoice: ScavengeChoiceSchema,
    processMining: ProcessMiningSchema,
    processForaging: ProcessForagingSchema,
};

export default exploreSchema;
