import { DisplayAvatar } from "@/components/DisplayAvatar";
import Spinner from "@/components/Spinners/Spinner";
import { capitaliseFirstLetter } from "@/helpers/capitaliseFirstLetter";
import { timeRemaining } from "@/helpers/dateHelpers";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQueryClient } from "@tanstack/react-query";
import clsx from "clsx";
import { useEffect } from "react";
import { Link } from "react-router-dom";
import HospitalisationReason from "../features/hospital/components/HospitalisationReason";
import { Pagination } from "./Pagination";
import Button from "@/components/Buttons/Button";
import { api } from "@/helpers/api";
import { useBailUser } from "@/features/jail/api/useBailUser";
import { useReviveUser } from "@/features/infirmary/api/useReviveUser";
import { formatCurrency } from "@/utils/currencyHelpers";

export const UsersTable = ({ data, isLoading, type, currentUser, reviveTalent, revivesRemaining, className }) => {
    const queryClient = useQueryClient();
    const bailUserMutation = useBailUser();
    const reviveUserMutation = useReviveUser();

    const tableHeadings = {
        faculty: {
            mobile: ["LVL", "TYPE", "CLASS"],
            desktop: ["LVL", "TYPE", "CLASS", "GANG"],
        },
        class: {
            mobile: ["LVL", "POINTS"],
            desktop: ["LVL", "POINTS"],
        },
        hospital: { mobile: ["REASON", "LENGTH"], desktop: ["REASON", "LENGTH"] },
        jail: {
            mobile: ["REASON", "LENGTH", "BAIL"],
            desktop: ["REASON", "LENGTH", "BAIL"],
        },
        bounties: {
            mobile: ["REWARD", "REASON", "PLACER"],
            desktop: ["REWARD", "REASON", "PLACER"],
        },
    };
    if (type === "hospital" && reviveTalent) {
        tableHeadings.hospital.mobile.push("REVIVE");
        tableHeadings.hospital.desktop.push("REVIVE");
    }
    const isMobile = useCheckMobileScreen();
    const headingType = isMobile ? "mobile" : "desktop";
    const currentHeadings = tableHeadings[type][headingType];

    const bailUser = (userID) => {
        bailUserMutation.mutate({ targetId: userID });
    };

    const reviveUser = (userID) => {
        reviveUserMutation.mutate({ targetId: userID });
    };

    // Currently fetching twice due to ghost hospital/jail users
    useEffect(() => {
        if (type === "hospital") {
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: api.infirmary.getHospitalList.key(),
                    }),
                100
            );
        }
        if (type === "jail") {
            setTimeout(
                () =>
                    queryClient.invalidateQueries({
                        queryKey: api.jail.jailList.key(),
                    }),
                100
            );
        }
    }, [type, queryClient]);

    if ((!data || data?.length === 0) && !isLoading) {
        let bountyText = "";
        if (type === "bounties") {
            bountyText = "The Bounty Board is currently empty.";
        } else {
            bountyText = `The ${capitaliseFirstLetter(type)} is currently empty.`;
        }
        return <p className="mt-5 text-center text-2xl dark:text-gray-200">{bountyText}</p>;
    }

    const reasonText = (reasonType, person) => {
        if (!person) {
            return "";
        }
        const regex = /(.*){{(.+?)}}/;
        if (reasonType === "jail" && person?.jailReason) {
            switch (person?.jailReason) {
                case "roguelike":
                    return `Caught littering`;
                case "scavenge":
                    return `Caught scavenging`;
                case "Assault":
                    return `Assaulting another student`;
                default:
                    return person?.jailReason;
            }
        }

        if (reasonType === "hospital" && person?.hospitalisedReason) {
            const match = person?.hospitalisedReason.match(regex);
            let actionType;
            let attacker;
            if (match) {
                actionType = match[1];
                attacker = match[2]?.replace("{{", "").replace("}}", "");
                if (/\d/.test(person?.hospitalisedReason)) {
                    return <HospitalisationReason actionType={actionType} attacker={attacker} />;
                }
                return `Beaten up by ${attacker} (Streets)`;
            } else {
                return person?.hospitalisedReason;
            }
        }
    };

    return (
        <>
            {/* <div className="filtercontainer mb-4 flex h-12 w-2/5 border dark:border-gray-600 dark:bg-gray-800 md:h-14 md:rounded-lg">
        <input
          className="mx-3 my-auto border-0 dark:bg-gray-800 text-slate-400"
          disabled
          type="text"
          placeholder="Search"
        />
      </div> */}
            <div className="flex flex-col">
                <div className="-my-2 sm:-mx-6 lg:-mx-8 overflow-x-auto">
                    <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <div
                            className={clsx(
                                "overflow-hidden border-gray-200 border-t sm:rounded-t-lg dark:border-gray-600",
                                className
                            )}
                        >
                            {isLoading ? (
                                <div className="flex size-full bg-white dark:bg-slate-800">
                                    <Spinner center />
                                </div>
                            ) : (
                                <table className="min-w-full divide-y divide-gray-200 border-gray-200 text-shadow md:border-x dark:divide-gray-600 dark:border-gray-600 ">
                                    <thead className="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <th
                                                scope="col"
                                                className="px-[4.3rem] py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider md:px-20 dark:text-gray-200"
                                            >
                                                {type !== "bounties" ? "Name" : "Target"}
                                            </th>
                                            {currentHeadings.map((heading, i) => (
                                                <th
                                                    key={i}
                                                    scope="col"
                                                    className="px-2 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider md:px-6 dark:text-gray-200"
                                                >
                                                    {heading}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>

                                    {/* // Temporary until tanstack table is implemented */}
                                    {type !== "bounties" ? (
                                        <tbody className="divide-y divide-gray-200 bg-white text-shadow dark:divide-gray-600 dark:bg-slate-800">
                                            {data.map((person) => (
                                                <tr key={person?.id}>
                                                    <td className="whitespace-nowrap px-3 py-4 md:px-6">
                                                        <div className="flex items-center">
                                                            <div className="size-10 shrink-0">
                                                                <DisplayAvatar
                                                                    className="size-10 rounded-full"
                                                                    src={person}
                                                                    loading="lazy"
                                                                />
                                                            </div>
                                                            <Link to={`/profile/${person?.id}`}>
                                                                <div className="ml-4">
                                                                    <div className="font-medium text-blue-600 text-sm text-stroke-sm md:text-base">
                                                                        {person?.username}{" "}
                                                                        <small className="block text-gray-500 text-xs md:hidden dark:text-gray-400">
                                                                            ID #{person?.id}
                                                                        </small>
                                                                    </div>
                                                                    <div className="hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-400">
                                                                        ID{" "}
                                                                        <span className="text-indigo-400">
                                                                            #{person?.id}
                                                                        </span>
                                                                    </div>
                                                                    {type === "faculty" && (
                                                                        <div className="mt-1 block text-gray-700 text-xs md:hidden md:text-sm dark:text-gray-300 dark:text-stroke-sm">
                                                                            {person?.gang === null
                                                                                ? "No Gang"
                                                                                : person?.gang.name}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </Link>
                                                        </div>
                                                    </td>
                                                    {type === "class" && (
                                                        <>
                                                            <td className="whitespace-nowrap px-2 py-4 md:px-6">
                                                                <div className="text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm">
                                                                    {person?.level || 1}
                                                                </div>
                                                            </td>
                                                            <td className="whitespace-nowrap px-2 py-4 md:px-6">
                                                                <div className="text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm">
                                                                    {person?.classPoints || 0}
                                                                </div>
                                                            </td>
                                                        </>
                                                    )}
                                                    {type === "faculty" ? (
                                                        <>
                                                            <td className="whitespace-nowrap px-2 py-4 md:px-6">
                                                                <div className="text-gray-900 text-xs md:text-sm dark:text-custom-yellow dark:text-stroke-sm">
                                                                    {person.level}
                                                                </div>
                                                            </td>
                                                            <td className="whitespace-nowrap px-2 py-4 md:px-6">
                                                                <div
                                                                    className={clsx(
                                                                        person?.userType === "admin"
                                                                            ? "text-red-600"
                                                                            : "text-green-600",
                                                                        "text-xs md:text-sm dark:text-stroke-sm"
                                                                    )}
                                                                >
                                                                    {person.userType === "admin"
                                                                        ? "Staff"
                                                                        : capitaliseFirstLetter(person.userType)}
                                                                </div>
                                                            </td>
                                                            <td className="whitespace-nowrap px-1 py-4 md:px-4">
                                                                <span className="inline-flex px-2 text-green-600 text-stroke-sm text-xs leading-5 md:text-sm dark:text-gray-200">
                                                                    {capitaliseFirstLetter(person.class)}
                                                                </span>
                                                            </td>
                                                            <td className="hidden whitespace-nowrap px-6 py-4 text-gray-500 text-xs md:table-cell md:text-sm dark:text-gray-200 dark:text-stroke-sm">
                                                                {person.gang === null ? "No Gang" : person.gang.name}
                                                            </td>
                                                        </>
                                                    ) : (
                                                        <>
                                                            {type !== "class" && (
                                                                <>
                                                                    {" "}
                                                                    <td className="whitespace-nowrap px-3 py-4 md:px-6">
                                                                        <div className="text-gray-900 text-xs md:text-sm dark:text-gray-200">
                                                                            {reasonText(type, person)}
                                                                        </div>
                                                                    </td>
                                                                    <td className="whitespace-nowrap px-5 py-4 md:px-6">
                                                                        <span className="inline-flex rounded-full bg-green-100 px-2 font-semibold text-green-800 text-xs leading-5 dark:bg-blue-700 dark:font-normal dark:text-custom-yellow dark:text-stroke-s-sm dark:ring-1 dark:ring-black">
                                                                            {type === "jail"
                                                                                ? timeRemaining(person.jailedUntil)
                                                                                : timeRemaining(
                                                                                      person.hospitalisedUntil
                                                                                  )}
                                                                        </span>
                                                                    </td>
                                                                </>
                                                            )}
                                                        </>
                                                    )}
                                                    {type === "jail" && (
                                                        <td className="whitespace-nowrap px-5 py-4 text-stroke-sm md:px-6 dark:text-gray-300">
                                                            <span className="inline-flex rounded-full px-2 font-semibold text-sm leading-5 dark:font-normal dark:text-amber-500">
                                                                {formatCurrency(person.level * 300)}
                                                            </span>
                                                            <Button
                                                                size="sm"
                                                                variant="flat"
                                                                className="block mt-1.5"
                                                                disabled={currentUser?.cash < person.level * 300}
                                                                onClick={() => bailUser(person.id)}
                                                            >
                                                                Bail
                                                            </Button>
                                                        </td>
                                                    )}
                                                    {type === "hospital" && reviveTalent && (
                                                        <td className="whitespace-nowrap px-5 py-4 text-stroke-sm md:px-6 dark:text-gray-300">
                                                            <Button
                                                                size="sm"
                                                                variant="flat"
                                                                className="block"
                                                                disabled={revivesRemaining === 0}
                                                                onClick={() => reviveUser(person.id)}
                                                            >
                                                                Revive ({revivesRemaining})
                                                            </Button>
                                                        </td>
                                                    )}
                                                </tr>
                                            ))}
                                        </tbody>
                                    ) : (
                                        <tbody className="divide-y divide-gray-200 bg-white text-shadow dark:divide-gray-600 dark:bg-slate-800">
                                            {data.map((person) => (
                                                <tr key={person?.id}>
                                                    <td className="whitespace-nowrap px-3 py-4 md:px-6">
                                                        <div className="flex items-center">
                                                            <div className="size-10 shrink-0">
                                                                <DisplayAvatar
                                                                    className="size-10 rounded-full"
                                                                    src={person?.target}
                                                                    loading="lazy"
                                                                />
                                                            </div>
                                                            <Link to={`/profile/${person?.target?.id}`}>
                                                                <div className="ml-4">
                                                                    <div className="font-medium text-blue-600 text-sm text-stroke-sm md:text-base">
                                                                        {person?.target?.username}{" "}
                                                                        <small className="block text-gray-500 text-xs md:hidden dark:text-gray-300">
                                                                            Student #{person?.target?.id}
                                                                        </small>
                                                                    </div>
                                                                    <div className="hidden text-gray-500 text-stroke-sm text-xs md:block md:text-sm dark:text-gray-300">
                                                                        Student{" "}
                                                                        <span className="text-indigo-400">
                                                                            #{person?.target?.id}
                                                                        </span>
                                                                    </div>
                                                                </div>
                                                            </Link>
                                                        </div>
                                                    </td>
                                                    <td className="whitespace-nowrap px-5 py-4 md:px-6">
                                                        <span className="inline-flex rounded-full bg-green-100 px-2 font-semibold text-green-800 text-xs leading-5 dark:bg-blue-700 dark:font-normal dark:text-custom-yellow dark:text-stroke-s-sm dark:ring-1 dark:ring-black">
                                                            {formatCurrency(person.amount)}
                                                        </span>
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 md:px-6">
                                                        <div className="text-gray-900 text-xs md:text-sm dark:text-gray-200">
                                                            {person.reason}
                                                        </div>
                                                    </td>
                                                    <td className="whitespace-nowrap px-3 py-4 md:px-6">
                                                        <div className="flex items-center">
                                                            <div className="size-10 shrink-0">
                                                                <DisplayAvatar
                                                                    className="size-10 rounded-full"
                                                                    src={person?.placer}
                                                                    loading="lazy"
                                                                />
                                                            </div>
                                                            <Link to={`/profile/${person?.placer?.id}`}>
                                                                <div className="ml-4">
                                                                    <div className="font-medium text-blue-600 text-sm text-stroke-sm md:text-base">
                                                                        {person?.placer?.username}{" "}
                                                                        <small
                                                                            className={clsx(
                                                                                person?.placerId === 5
                                                                                    ? "text-red-500"
                                                                                    : "text-gray-300",
                                                                                "block text-xs md:hidden"
                                                                            )}
                                                                        >
                                                                            {person?.placerId === 5 ? (
                                                                                "Disciplinary Overseer"
                                                                            ) : (
                                                                                <>
                                                                                    Student{" "}
                                                                                    <span className="text-indigo-400">
                                                                                        #{person?.placer?.id}
                                                                                    </span>
                                                                                </>
                                                                            )}
                                                                        </small>
                                                                    </div>
                                                                    <div
                                                                        className={clsx(
                                                                            person?.placerId === 5
                                                                                ? "text-red-500"
                                                                                : "text-gray-300",
                                                                            "hidden text-stroke-sm text-xs md:block md:text-sm"
                                                                        )}
                                                                    >
                                                                        {person?.placerId === 5 ? (
                                                                            "Disciplinary Overseer"
                                                                        ) : (
                                                                            <>
                                                                                Student{" "}
                                                                                <span className="text-indigo-400">
                                                                                    #{person?.placer?.id}
                                                                                </span>
                                                                            </>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </Link>
                                                        </div>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    )}
                                </table>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            <Pagination data={data} />
        </>
    );
};
