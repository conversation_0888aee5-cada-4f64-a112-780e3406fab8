import { db } from "../lib/db.js";

export const findActiveLottery = async () => {
    return await db.lottery.findFirst({
        where: { winnerId: null },
        orderBy: { id: "desc" },
    });
};

export const findLotteryByDate = async (date: Date) => {
    return await db.lottery.findFirst({
        where: { drawDate: date },
    });
};

export const findLotteryEntry = async (userId: number, lotteryId: number) => {
    return await db.lottery_entry.findFirst({
        where: { userId, lotteryId },
    });
};

export const createLottery = async (drawDate: Date) => {
    return await db.lottery.create({
        data: { drawDate },
    });
};

export const createLotteryEntry = async (userId: number, lotteryId: number) => {
    return await db.lottery_entry.create({
        data: { userId, lotteryId },
    });
};

export const findLotteryWithoutWinner = async (date: Date) => {
    return await db.lottery.findFirst({
        where: {
            drawDate: date,
            winnerId: null,
        },
    });
};

export const findLotteryEntriesWithUsers = async (lotteryId: number) => {
    return await db.lottery_entry.findMany({
        where: { lotteryId },
        include: { user: true },
        orderBy: { id: "desc" },
    });
};

export const updateLotteryWinner = async (
    lotteryId: number,
    winnerId: number | undefined,
    entries: number,
    additionalPrize = 0
) => {
    return await db.lottery.update({
        where: { id: lotteryId },
        data: {
            winnerId,
            entries,
            ...(additionalPrize > 0 && { prizeAmount: { increment: additionalPrize } }),
        },
    });
};
