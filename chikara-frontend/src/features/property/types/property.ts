import { AppRouterClient } from "@/lib/orpc";

export type Property = Awaited<ReturnType<AppRouterClient["property"]["getHousingList"]>>[number];
export type UserProperty = Awaited<ReturnType<AppRouterClient["property"]["getUserProperties"]>>[number];

export interface PropertyPurchaseParams {
    propertyId: number;
}

export interface PropertySellParams {
    propertyId: number;
}

export interface PropertySetPrimaryParams {
    propertyId: number;
}
