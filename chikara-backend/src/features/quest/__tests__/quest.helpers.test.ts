import { describe, it, expect, vi, beforeEach } from "vitest";
import { QuestProgressStatus } from "@prisma/client";
import { UserCanStartQuest } from "../quest.helpers.js";
import * as QuestRepository from "../../../repositories/quest.repository.js";

// Mock the repository
vi.mock("../../../repositories/quest.repository.js");

const mockQuestRepository = vi.mocked(QuestRepository);

describe("Quest Helpers", () => {
    const mockUser = {
        id: 1,
        level: 5,
        username: "testuser",
        cash: 1000,
    } as any;

    const mockQuest = {
        id: 1,
        name: "Test Quest",
        levelReq: 3,
        disabled: false,
        requiredQuestId: null,
    } as any;

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("UserCanStartQuest", () => {
        it("should return true when all requirements are met", async () => {
            mockQuestRepository.getUserQuestProgress.mockResolvedValue(null);

            const result = await UserCanStartQuest(mockUser, mockQuest);

            expect(result).toEqual({ success: true });
        });

        it("should return false when user already has progress on quest", async () => {
            mockQuestRepository.getUserQuestProgress.mockResolvedValue({
                id: 1,
                userId: 1,
                questId: 1,
                questStatus: QuestProgressStatus.in_progress,
                createdAt: new Date(),
                updatedAt: new Date(),
            } as any);

            const result = await UserCanStartQuest(mockUser, mockQuest);

            expect(result).toEqual({ error: "User has already started this quest", statusCode: 400 });
        });

        it("should return false when quest is disabled", async () => {
            const disabledQuest = { ...mockQuest, disabled: true };
            mockQuestRepository.getUserQuestProgress.mockResolvedValue(null);

            const result = await UserCanStartQuest(mockUser, disabledQuest);

            expect(result).toEqual({ error: "Quest is disabled", statusCode: 400 });
        });

        it("should return false when user level is too low", async () => {
            const lowLevelUser = { ...mockUser, level: 1 };
            const highLevelQuest = { ...mockQuest, levelReq: 10 };
            mockQuestRepository.getUserQuestProgress.mockResolvedValue(null);

            const result = await UserCanStartQuest(lowLevelUser, highLevelQuest);

            expect(result).toEqual({ error: "User does not meet level requirement", statusCode: 400 });
        });

        it("should return false when prerequisite quest is not completed", async () => {
            const questWithPrereq = { ...mockQuest, requiredQuestId: 2 };
            mockQuestRepository.getUserQuestProgress
                .mockResolvedValueOnce(null) // For the main quest
                .mockResolvedValueOnce(null); // For the prerequisite quest

            const result = await UserCanStartQuest(mockUser, questWithPrereq);

            expect(result).toEqual({ error: "Prerequisite quest not completed", statusCode: 400 });
        });

        it("should return false when prerequisite quest is not completed (in progress)", async () => {
            const questWithPrereq = { ...mockQuest, requiredQuestId: 2 };
            mockQuestRepository.getUserQuestProgress
                .mockResolvedValueOnce(null) // For the main quest
                .mockResolvedValueOnce({
                    id: 2,
                    userId: 1,
                    questId: 2,
                    questStatus: QuestProgressStatus.in_progress,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                } as any); // For the prerequisite quest

            const result = await UserCanStartQuest(mockUser, questWithPrereq);

            expect(result).toEqual({ error: "Prerequisite quest not completed", statusCode: 400 });
        });

        it("should return true when prerequisite quest is completed", async () => {
            const questWithPrereq = { ...mockQuest, requiredQuestId: 2 };
            mockQuestRepository.getUserQuestProgress
                .mockResolvedValueOnce(null) // For the main quest
                .mockResolvedValueOnce({
                    id: 2,
                    userId: 1,
                    questId: 2,
                    questStatus: QuestProgressStatus.complete,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                } as any); // For the prerequisite quest

            const result = await UserCanStartQuest(mockUser, questWithPrereq);

            expect(result).toEqual({ success: true });
        });
    });
});
