import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";

/**
 * Find all shops in the database
 */
export const findAllShops = async () => {
    return await db.shop.findMany();
};

/**
 * Find a shop by ID with all its listings and item details
 */
export const findShopById = async (shopId: number) => {
    return await db.shop.findUnique({
        where: { id: shopId },
        include: {
            shop_listing: {
                include: {
                    item: {
                        select: {
                            id: true,
                            name: true,
                            itemType: true,
                            rarity: true,
                            level: true,
                            about: true,
                            cashValue: true,
                            image: true,
                            damage: true,
                            armour: true,
                            health: true,
                            energy: true,
                            actionPoints: true,
                            baseAmmo: true,
                            itemEffects: true,
                            recipeUnlockId: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Find a shop listing by ID
 */
export const findShopListingById = async (listingId: number) => {
    return await db.shop_listing.findUnique({
        where: { id: listingId },
    });
};

/**
 * Update shop listing stock
 */
export const updateShopListingStock = async (listingId: number, newStock: number) => {
    return await db.shop_listing.update({
        where: { id: listingId },
        data: { stock: newStock },
    });
};

/**
 * Create a new shop
 */
export const createShop = async (data: Prisma.shopCreateInput) => {
    return await db.shop.create({ data });
};

/**
 * Update shop data
 */
export const updateShop = async (shopId: number, data: Prisma.shopUpdateInput) => {
    return await db.shop.update({
        where: { id: shopId },
        data,
    });
};

/**
 * Delete a shop
 */
export const deleteShop = async (shopId: number) => {
    return await db.shop.delete({
        where: { id: shopId },
    });
};

/**
 * Create a new shop listing
 */
export const createShopListing = async (data: Prisma.shop_listingCreateInput) => {
    return await db.shop_listing.create({ data });
};

/**
 * Update shop listing
 */
export const updateShopListing = async (listingId: number, data: Prisma.shop_listingUpdateInput) => {
    return await db.shop_listing.update({
        where: { id: listingId },
        data,
    });
};

/**
 * Delete a shop listing
 */
export const deleteShopListing = async (listingId: number) => {
    return await db.shop_listing.delete({
        where: { id: listingId },
    });
};

/**
 * Get trader reputation
 */
export const getTraderReputation = async (filter: { userId: number; shopId?: number }) => {
    return await db.trader_rep.findMany({
        where: filter,
    });
};
