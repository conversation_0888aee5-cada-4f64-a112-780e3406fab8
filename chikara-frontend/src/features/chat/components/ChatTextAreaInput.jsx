import emojiImg from "@/assets/icons/UI/emojiButton.png";
import sendImg from "@/assets/icons/UI/sendButton.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { api } from "@/helpers/api";
import useOuterClick from "@/hooks/useOuterClick";
import { cn } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { formatDistanceToNowStrict } from "date-fns";
import { AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import React, { useState, useEffect, useRef, useCallback } from "react";
import { toast } from "react-hot-toast";
import { countEmotesInMessage } from "../helpers/chatHelpers";
import EmotePicker from "./EmotePicker";
import RenderChatText from "./RenderChatText";

export default function ChatTextAreaInput({
    chatBannedUntil,
    socket,
    scrollToBottom,
    chatRoom,
    currentUser,
    selectedReplyMessage = null,
    setSelectedReplyMessage,
    focusChatMsgInput,
    setFocusChatMsgInput,
}) {
    const [userMessage, setUserMessage] = useState("");
    const [openEmoji, setOpenEmoji] = useState(false);
    const [messageBlocked, setMessageBlocked] = useState(0);
    const [noMessageError, setNoMessageError] = useState(false);
    const queryClient = useQueryClient();
    const chatMsgInputRef = useRef(null);

    useEffect(() => {
        if (focusChatMsgInput) {
            setTimeout(() => {
                chatMsgInputRef.current?.focus();
                setFocusChatMsgInput(false);
            }, 100);
        }
    }, [focusChatMsgInput]);

    const innerRef = useOuterClick(() => {
        setOpenEmoji(false);
    });

    const handleSubmit = useCallback(() => {
        if (chatBannedUntil > 0) {
            queryClient.invalidateQueries({
                queryKey: api.user.getCurrentUserInfo.key(),
            });
            toast.error(`You are chatbanned for ${formatDistanceToNowStrict(new Date(chatBannedUntil))}!`);
            return;
        }

        if (messageBlocked >= 3) {
            toast.error("You are sending messages too fast!");
            return;
        }

        if (userMessage.length > 0) {
            if (countEmotesInMessage(userMessage) > 10) {
                toast.error("Too many emojis at once!");
                if (!noMessageError) {
                    setNoMessageError(true);
                    setTimeout(() => setNoMessageError(false), 1500);
                }
                return;
            }

            setMessageBlocked((current) => current + 1);
            setTimeout(() => {
                setMessageBlocked((current) => Math.max(0, current - 1));
            }, 5000);

            const submittedMessage = userMessage;

            const parentMessageId = selectedReplyMessage?.id || null;

            socket.emit("chat message", {
                message: submittedMessage,
                room: chatRoom,
                parentMessageId,
            });

            setSelectedReplyMessage(null);
            setUserMessage("");
            scrollToBottom(true);
        } else {
            setNoMessageError(true);
            toast.error("Message can't be blank!");
            setTimeout(() => setNoMessageError(false), 2000);
        }
    }, [
        chatBannedUntil,
        messageBlocked,
        userMessage,
        queryClient,
        socket,
        chatRoom,
        selectedReplyMessage,
        setSelectedReplyMessage,
        scrollToBottom,
        noMessageError,
    ]);

    const handleKeyDown = useCallback(
        (event) => {
            if (event.key === "Enter") {
                event.preventDefault();
                handleSubmit();
            }
        },
        [handleSubmit]
    );

    const scrollToMessage = (messageId) => {
        const element = document.getElementById(`message-${messageId}`);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    const handleMessageChange = useCallback((e) => setUserMessage(e.target.value), []);

    return (
        <div className="my-3">
            <div
                className={cn(
                    "relative flex w-full flex-col rounded-md border border-gray-300 bg-[#dee2e9] shadow-xs sm:text-sm dark:bg-[#15121C]",
                    noMessageError ? "chatMessageBoxError" : "chatMessageBox"
                )}
            >
                {selectedReplyMessage && (
                    <div className="mx-1 mt-1 mb-0.5 flex w-[96%] flex-row truncate rounded-xs border-custom-yellow border-l-4 bg-blue-950 px-1 font-body text-gray-300 text-xs ring-1 ring-custom-yellow">
                        <DisplayAvatar src={selectedReplyMessage?.user} className="mr-1 h-7 w-auto rounded-md p-1" />
                        <div
                            className="my-auto w-[78%] cursor-pointer truncate py-1 italic"
                            onClick={() => scrollToMessage(selectedReplyMessage.id)}
                        >
                            <RenderChatText
                                msg={selectedReplyMessage}
                                className="my-auto w-[90%] break-words font-body text-gray-700 text-sm md:text-[0.8rem] md:leading-[1.2rem] dark:text-gray-200"
                                imgClassName="max-h-5"
                            />
                        </div>
                        <X
                            className="my-auto ml-auto size-[1.15rem] cursor-pointer rounded-md border border-gray-400 bg-black text-red-500 hover:text-red-300"
                            onClick={() => setSelectedReplyMessage(null)}
                        />
                    </div>
                )}
                <div className="flex flex-row">
                    <textarea
                        ref={chatMsgInputRef}
                        required
                        id="chatMessage"
                        name="chatMessage"
                        maxLength={200}
                        rows={2}
                        cols={1}
                        placeholder="Your message..."
                        value={userMessage}
                        className={cn(
                            "chatTextArea scrollbar rounded-md border-none bg-[#dee2e9] p-2 font-body text-sm dark:bg-[#15121C] dark:text-white"
                            // selectedReplyMessage && "pt-10",
                        )}
                        onChange={handleMessageChange}
                        onKeyDown={handleKeyDown}
                    />
                    <div
                        className="relative mx-1 my-auto"
                        onClick={(e) => {
                            e.stopPropagation();
                            setOpenEmoji((prev) => !prev);
                        }}
                    >
                        <ButtonWrapper>
                            <img src={emojiImg} alt="" className="size-6 fill-white text-white" />
                        </ButtonWrapper>
                    </div>
                    <AnimatePresence>
                        {openEmoji && (
                            <EmotePicker
                                userMessage={userMessage}
                                setUserMessage={setUserMessage}
                                innerRef={innerRef}
                                currentUser={currentUser}
                            />
                        )}
                    </AnimatePresence>
                    <div className="relative mx-1 my-auto" onClick={handleSubmit}>
                        <ButtonWrapper>
                            <img src={sendImg} alt="" className="h-5 w-4 fill-white text-white" />
                        </ButtonWrapper>
                    </div>
                </div>
            </div>
        </div>
    );
}

const ButtonWrapper = React.memo(({ children }) => (
    <button className="size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28287c] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110">
        <div className="flex items-center justify-center">{children}</div>
    </button>
));
