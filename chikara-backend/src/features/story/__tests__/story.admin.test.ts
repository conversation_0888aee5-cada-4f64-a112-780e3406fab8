import { describe, it, expect, vi, beforeEach } from "vitest";
import * as StoryAdminService from "../story.admin.js";

// Mock dependencies
vi.mock("../../../repositories/story.repository.js");
vi.mock("../story.controller.js");

describe("Story Admin Service", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("validateEpisodeContent", () => {
        it("should validate valid episode content", () => {
            const validContent = {
                scenes: [
                    {
                        id: 1,
                        type: "dialogue",
                        text: "Hello world",
                    },
                ],
            };

            const result = StoryAdminService.validateEpisodeContent(validContent);

            expect(result.isValid).toBe(true);
            expect(result.errors).toHaveLength(0);
        });

        it("should detect invalid episode content", () => {
            const invalidContent = {
                scenes: [
                    {
                        // Missing id, type, and text
                    },
                ],
            };

            const result = StoryAdminService.validateEpisodeContent(invalidContent);

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Scene 1: Missing scene ID");
            expect(result.errors).toContain("Scene 1: Missing scene type");
            expect(result.errors).toContain("Scene 1: Missing scene text");
        });

        it("should handle missing content", () => {
            const result = StoryAdminService.validateEpisodeContent(null);

            expect(result.isValid).toBe(false);
            expect(result.errors).toContain("Content is required");
        });
    });
});
