import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";

export const recordBattleOutcome = async (attackerId: string, defenderId: string, victory: boolean) => {
    await db.battle_log.create({
        data: {
            attackerId: Number.parseInt(attackerId),
            defenderId: Number.parseInt(defenderId),
            victory,
        },
    });
};

export const countBattleWinsAgainstTarget = async (attackerId: number, defenderId: number, today: Date) => {
    return await db.battle_log.count({
        where: {
            attackerId: attackerId,
            defenderId: defenderId,
            victory: true,
            createdAt: {
                gte: today,
            },
        },
    });
};

export const findUserItemById = async (itemId: number) => {
    return await db.user_item.findUnique({
        where: {
            id: itemId,
        },
        include: {
            item: true,
        },
    });
};

export const findBountyByTargetId = async (targetId: number) => {
    return await db.bounty.findFirst({
        where: {
            targetId: targetId,
        },
    });
};
