import { Rival } from "@/features/social/types/social";
import { api } from "@/helpers/api";
import { UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface AddRivalParams {
    userId: number;
}

const useAddRival = (options: Partial<UseMutationOptions<Rival, Error, AddRivalParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.addRival.mutationOptions({
            onSuccess: () => {
                toast.success("Rival added successfully!");
                // Invalidate rivals list to refresh the data
                queryClient.invalidateQueries({ queryKey: api.social.getRivals.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to add rival");
            },
            ...options,
        })
    );
};

export default useAddRival;
