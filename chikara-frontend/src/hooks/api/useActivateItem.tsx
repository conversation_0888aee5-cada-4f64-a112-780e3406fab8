import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

/**
 * Custom hook to use an item
 */
export const useActivateItem = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        // eslint-disable-next-line react-hooks/react-compiler
        api.user.useItem.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getCurrentUserInfo.key() });
                queryClient.invalidateQueries({ queryKey: api.user.getStatusEffects.key() });
                toast.success("Item used successfully!");

                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error: Error) => {
                toast.error(error.message || "Failed to use item");
            },
        })
    );
};

export default useActivateItem;
