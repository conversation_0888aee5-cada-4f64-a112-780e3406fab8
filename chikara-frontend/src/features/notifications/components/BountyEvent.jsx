import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Sparkles } from "lucide-react";

import { Link } from "react-router-dom";
import { formatCurrency } from "@/utils/currencyHelpers";

function BountyEvent({ details, read, type }) {
    const { data: currentUser } = useFetchCurrentUser();

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            {type === "bounty_claimed" ? (
                <BountyClaimed details={details} read={read} currentUser={currentUser} />
            ) : (
                <BountyPlaced details={details} read={read} />
            )}
        </>
    );
}

export default BountyEvent;

const BountyClaimed = ({ details, read, currentUser }) => {
    const yourBounty = details.placerId === currentUser.id;
    const { data: user } = useGetUserInfo(details.targetId, {
        staleTime: 120000,
    });
    const { data: claimedBy } = useGetUserInfo(details.claimedById, {
        enabled: yourBounty,
        staleTime: 120000,
    });

    if (yourBounty) {
        return (
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                Your bounty of{" "}
                <span className="text-indigo-500 text-stroke-sm">{formatCurrency(details?.bountyAmount || 0)} </span>
                on{" "}
                <Link className="text-blue-600" to={`/profile/${user?.id}`}>
                    {user?.username}
                </Link>{" "}
                was claimed by{" "}
                <Link className="text-blue-600" to={`/profile/${claimedBy?.id}`}>
                    {claimedBy?.username}
                </Link>
            </td>
        );
    }

    return (
        <td
            className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
        >
            You claimed the bounty of{" "}
            <span className="text-indigo-500 text-stroke-sm">{formatCurrency(details?.bountyAmount || 0)} </span>
            from defeating{" "}
            <Link className="text-blue-600" to={`/profile/${user?.id}`}>
                {user?.username}
            </Link>
        </td>
    );
};

const BountyPlaced = ({ details, read }) => {
    const { data: user } = useGetUserInfo(details.placerId, {
        staleTime: 120000,
    });
    return (
        <td
            className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
        >
            A bounty of{" "}
            <span className="text-indigo-500 text-stroke-sm">{formatCurrency(details?.bountyAmount || 0)} </span>
            was placed on you by{" "}
            <Link className="text-blue-600" to={`/profile/${user?.id}`}>
                {user?.username}
            </Link>
        </td>
    );
};
