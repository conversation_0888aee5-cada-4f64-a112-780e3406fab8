import { getUserItemCount } from "@/helpers/getUserItemCount";
import type { InventoryItem } from "@/types/item";
import { QuestObjectiveTypes, type QuestObjectiveWithProgress } from "@/types/quest";

export const getObjectiveProgress = (objective: QuestObjectiveWithProgress, inventory: InventoryItem[]) => {
    if (!objective.quest_objective_progress || objective.quest_objective_progress.length === 0) {
        return 0;
    }

    if (objective.objectiveType === QuestObjectiveTypes.ACQUIRE_ITEM) {
        const objProgressCount = objective.quest_objective_progress.reduce((acc, curr) => acc + curr.count, 0);
        if (objProgressCount > 0) {
            return objProgressCount;
        }
        const itemCount = getUserItemCount(inventory, objective.itemId || 0);
        return Math.min(itemCount, objective.quantity || 0);
    }

    if (objective.objectiveType === QuestObjectiveTypes.GATHER_RESOURCES) {
        const objProgressCount = objective.quest_objective_progress.reduce((acc, curr) => acc + curr.count, 0);
        return objProgressCount;
    }

    return objective.quest_objective_progress.reduce((acc, curr) => acc + curr.count, 0);
};
