import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";

/**
 * Find user friends list with relationship details
 */
export const getUserFriends = async (userId: number) => {
    return await db.friend.findMany({
        where: { userId },
        include: {
            friend: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    last_activity: true,
                    statusMessage: true,
                    statusMessageUpdatedAt: true,
                    showLastOnline: true,
                    gangId: true,
                    jailedUntil: true,
                    hospitalisedUntil: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Find incoming friend requests for a user
 */
export const getFriendRequests = async (userId: number) => {
    return await db.friend_request.findMany({
        where: { receiverId: userId },
        include: {
            sender: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    avatar: true,
                    gangId: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Check if a friendship exists between two users
 */
export const findExistingFriendship = async (userId1: number, userId2: number) => {
    return await db.friend.findFirst({
        where: {
            OR: [
                { userId: userId1, friendId: userId2 },
                { userId: userId2, friendId: userId1 },
            ],
        },
    });
};

/**
 * Check if a friend request exists between two users
 */
export const findExistingFriendRequest = async (userId1: number, userId2: number) => {
    return await db.friend_request.findFirst({
        where: {
            OR: [
                { senderId: userId1, receiverId: userId2 },
                { senderId: userId2, receiverId: userId1 },
            ],
        },
    });
};

/**
 * Create a new friend request
 */
export const createFriendRequest = async (senderId: number, receiverId: number) => {
    return await db.friend_request.create({
        data: {
            senderId,
            receiverId,
        },
    });
};

/**
 * Find a specific friend request by ID for a user
 */
export const findFriendRequestById = async (requestId: number, userId: number) => {
    return await db.friend_request.findFirst({
        where: {
            id: requestId,
            receiverId: userId,
        },
    });
};

/**
 * Delete a friend request
 */
export const deleteFriendRequest = async (requestId: number) => {
    return await db.friend_request.delete({
        where: { id: requestId },
    });
};

/**
 * Create a friendship (one direction)
 */
export const createFriendship = async (userId: number, friendId: number) => {
    return await db.friend.create({
        data: {
            userId,
            friendId,
        },
    });
};

/**
 * Find a friendship by user and friend IDs
 */
export const findFriendship = async (userId: number, friendId: number) => {
    return await db.friend.findFirst({
        where: { userId, friendId },
    });
};

/**
 * Get detailed friendship data
 */
export const getFriendshipDetails = async (userId: number, friendId: number) => {
    return await db.friend.findFirst({
        where: { userId, friendId },
        include: {
            friend: {
                select: {
                    username: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Delete friendships between two users (both directions)
 */
export const deleteFriendships = async (userId1: number, userId2: number) => {
    return await db.friend.deleteMany({
        where: {
            OR: [
                { userId: userId1, friendId: userId2 },
                { userId: userId2, friendId: userId1 },
            ],
        },
    });
};

/**
 * Update note on a friendship
 */
export const updateFriendshipNote = async (friendshipId: number, note: string | null) => {
    return await db.friend.update({
        where: { id: friendshipId },
        data: { note },
    });
};

/**
 * Update user's status message
 */
export const updateUserStatusMessage = async (userId: number, message: string | null) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            statusMessage: message,
            statusMessageUpdatedAt: message ? getNow() : null,
        },
        select: {
            statusMessage: true,
            statusMessageUpdatedAt: true,
        },
    });
};

/**
 * Update user's privacy settings
 */
export const updateUserPrivacySettings = async (userId: number, data: Record<string, boolean>) => {
    return await db.user.update({
        where: { id: userId },
        data,
        select: {
            showLastOnline: true,
        },
    });
};

/**
 * Get user's rivals list with relationship details
 */
export const getUserRivals = async (userId: number) => {
    return await db.rival.findMany({
        where: { userId },
        include: {
            rival: {
                select: {
                    id: true,
                    username: true,
                    level: true,
                    gangId: true,
                    avatar: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                    targetedBounties: {
                        where: { active: true },
                        select: {
                            id: true,
                            amount: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Find if a rival relationship exists
 */
export const findExistingRival = async (userId: number, rivalId: number) => {
    return await db.rival.findFirst({
        where: { userId, rivalId },
    });
};

/**
 * Create a rival relationship
 */
export const createRival = async (userId: number, rivalId: number) => {
    return await db.rival.create({
        data: {
            userId,
            rivalId,
        },
        include: {
            rival: {
                select: {
                    username: true,
                    avatar: true,
                    gangId: true,
                    gang: {
                        select: {
                            name: true,
                        },
                    },
                },
            },
        },
    });
};

/**
 * Delete a rival relationship
 */
export const deleteRival = async (rivalId: number) => {
    return await db.rival.delete({
        where: { id: rivalId },
    });
};

/**
 * Update note on a rival
 */
export const updateRivalNote = async (rivalId: number, note: string | null) => {
    return await db.rival.update({
        where: { id: rivalId },
        data: { note },
    });
};
