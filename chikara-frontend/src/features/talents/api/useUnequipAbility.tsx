import { useMutation, useQueryClient, type UseMutationOptions } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import toast from "react-hot-toast";
import type { Ability } from "../types/talents";

type AbilitySlot = 1 | 2 | 3 | 4;
const MAX_SLOTS = 4;

interface UnequipAbilityParams {
    slot: AbilitySlot;
}

interface UnequipAbilityOptions {
    onClose?: () => void;
    successMessage?: string;
}

const useUnequipAbility = (
    { onClose, successMessage }: UnequipAbilityOptions = {},
    options?: UseMutationOptions<any, Error, UnequipAbilityParams>
) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.talents.unequipAbility.mutationOptions({
            onMutate: async (variables) => {
                const equippedQueryOptions = api.talents.getEquippedAbilities.queryOptions();
                await queryClient.cancelQueries(equippedQueryOptions);

                const previousData = queryClient.getQueryData<(Ability | null)[]>(
                    api.talents.getEquippedAbilities.key()
                );

                const updatedData = previousData ? [...previousData] : Array(MAX_SLOTS).fill(null);
                if (variables.slot < 1 || variables.slot > MAX_SLOTS) {
                    throw new Error(`Invalid slot: ${variables.slot}`);
                }
                updatedData[variables.slot - 1] = null;
                queryClient.setQueryData(api.talents.getEquippedAbilities.key(), updatedData);

                return { previousData };
            },
            onError: (error: any, _vars, context: any) => {
                // Revert to previous data if the mutation fails
                if (context?.previousData) {
                    queryClient.setQueryData(api.talents.getEquippedAbilities.key(), context.previousData);
                }

                toast.error(error.response?.data?.error || "Failed to unequip ability");
            },
            onSuccess: (_data, variables) => {
                const defaultMessage = successMessage || `Ability unequipped from slot ${variables.slot}`;
                toast.success(defaultMessage);
                onClose?.();
            },
            onSettled: () => {
                const equippedQueryOptions = api.talents.getEquippedAbilities.queryOptions();
                queryClient.invalidateQueries(equippedQueryOptions);
                queryClient.invalidateQueries({ queryKey: api.talents.getUnlockedTalents.key() });
            },
            ...options,
        })
    );
};

export default useUnequipAbility;
