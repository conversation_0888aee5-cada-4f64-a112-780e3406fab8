import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

/**
 * Request parameters for starting a crafting operation
 */
export interface StartCraftParams {
    recipeId: number;
    amount?: number;
}

/**
 * Custom hook to initiate a crafting operation using ORPC
 * @param onSuccessCallback - Optional callback function to execute on successful craft
 */
const useStartCraft = (onSuccessCallback?: () => void) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.crafting.craftItem.mutationOptions({
            onSuccess: () => {
                toast.success("Crafting started!");

                // Invalidate relevant queries to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.crafting.getCraftingQueue.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: api.user.getInventory.key(),
                });

                // Execute additional callback if provided
                if (onSuccessCallback) {
                    onSuccessCallback();
                }
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error("Crafting error:", errorMessage);
                toast.error(errorMessage);
            },
        })
    );
};

export default useStartCraft;
