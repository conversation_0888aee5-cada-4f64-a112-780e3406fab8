import Button from "@/components/Buttons/Button";
import { DisplayGangIcon } from "@/components/DisplayGangIcon";
import LoadingState from "@/components/LoadingState";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { Info } from "lucide-react";
import { useState } from "react";
import { Link } from "react-router-dom";
import useAcceptGangInvite from "../features/gang/api/useAcceptGangInvite";
import useDeclineGangInvite from "../features/gang/api/useDeclineGangInvite";
import CreateGangModal from "../features/gang/components/CreateGangModal";
import YourGang from "../features/gang/components/YourGang";

export default function Gang() {
    const [open, setOpen] = useState(false);
    const { data: currentUser } = useFetchCurrentUser();
    const { data: hasGangItem } = useQuery(
        api.gang.hasGangSigil.queryOptions({
            enabled: !currentUser?.gangId,
        })
    );

    return (
        <>
            {currentUser?.gangId ? (
                <YourGang currentUser={currentUser} />
            ) : (
                <>
                    {" "}
                    <CreateGangModal
                        open={open}
                        setOpen={setOpen}
                        text="Create a Gang"
                        subtitle="Create a gang to join"
                        level={currentUser?.level}
                        hasGangItem={hasGangItem}
                    />
                    <section className="mx-auto rounded-lg bg-gray-100 py-6 md:max-w-3xl dark:bg-gray-800">
                        <div className="container mx-auto px-0 md:px-6">
                            <div className="mx-auto max-w-xl text-center">
                                <div className="-mx-5 rounded-lg border border-gray-600 bg-white py-5 text-left shadow-xl md:mx-0 md:p-6 dark:bg-gray-900">
                                    <div className="flex flex-col items-center justify-center">
                                        <div className="w-full px-4">
                                            {" "}
                                            <p className="text-center">You are not in a gang!</p>
                                            <div className="mt-4 flex items-center justify-center gap-4">
                                                <Link to="/ganglist">
                                                    <Button>All Gangs</Button>
                                                </Link>
                                                <Button onClick={() => setOpen(true)}>Form a Gang</Button>
                                            </div>
                                            <GangInvites />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </>
            )}
        </>
    );
}

const GangInvites = () => {
    const { data: currentInvites, isLoading } = useQuery(api.gang.getCurrentInvites.queryOptions());
    const { acceptInvite } = useAcceptGangInvite();
    const { declineInvite } = useDeclineGangInvite();

    const getMaxMemberCount = (hideoutLevel) => {
        // -- TEMP FOR ALPHA --
        if (hideoutLevel === 0) {
            return 5;
        } else {
            return 7;
        }
        // -- TEMP FOR ALPHA --
        // return 5 + hideoutLevel * 3;
    };

    if (isLoading) return null;

    return (
        <>
            <div className="mt-4 flex w-full flex-col items-center">
                <hr className="mb-2 w-full border border-gray-600/75" />
                <p className="mb-2">Your invites:</p>
                <LoadingState isLoading={isLoading}>
                    {currentInvites?.length > 0 ? (
                        <div className=" flex w-full flex-col justify-center gap-2 text-center text-custom-yellow text-sm">
                            {currentInvites?.map((invite) => (
                                <div key={invite.id} className="flex w-full gap-2">
                                    <div className="my-auto mb-1 flex h-14 w-3/4 items-center gap-2 rounded-lg bg-blue-800 p-2 md:py-2.5">
                                        <DisplayGangIcon src={invite.gang} className="rounded-md! my-auto h-6" />
                                        <div className="text-left">
                                            <p className="truncate">{invite.gang.name}</p>
                                            <p className="text-gray-200 text-xs">
                                                {invite.gang.memberCount}/
                                                {getMaxMemberCount(invite?.gang?.hideout_level)} Members
                                            </p>
                                        </div>
                                        <div className="ml-auto">
                                            <img
                                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/iQwXWBw.png`}
                                                alt=""
                                                className="m-auto size-8 grayscale"
                                            />
                                            <p className="text-center text-gray-200 text-xs leading-none">Unranked</p>
                                        </div>
                                    </div>
                                    <div className="mt-1.5 flex w-2/5 flex-1 gap-2">
                                        <Button className="text-sm!" onClick={() => acceptInvite(invite.id)}>
                                            Accept
                                        </Button>
                                        <Button
                                            className="text-sm!"
                                            variant="destructive"
                                            onClick={() => declineInvite(invite.id)}
                                        >
                                            Decline
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className=" flex w-full justify-center gap-2 text-center text-amber-500 text-sm">
                            <span className="-ml-3 my-auto text-amber-400">
                                {" "}
                                <Info />
                            </span>
                            No invites yet. Find a Gang in the Gangs list!
                        </p>
                    )}
                </LoadingState>
            </div>
        </>
    );
};
