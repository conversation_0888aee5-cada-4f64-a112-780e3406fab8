import Button from "@/components/Buttons/Button";
import news from "@/constants/news";
import useSetNewsIDRead from "@/features/news/api/useSetNewsIDRead";
import ChangelogCard from "@/features/news/components/ChangelogCard";
import PostContent from "@/features/news/components/PostContent";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { ArrowLeft } from "lucide-react";
import { useEffect } from "react";
import { Link, useParams } from "react-router-dom";

const LatestNews = () => {
    const { postID } = useParams();
    const selectedPost = postID ? news.find((post) => post.href === postID) : null;
    const { data: currentUser } = useFetchCurrentUser();
    const { setNewsID } = useSetNewsIDRead();

    useEffect(() => {
        if (currentUser && currentUser?.lastNewsIDRead < news[0].id) {
            setNewsID.mutate(news[0].id);
        }
    }, [currentUser]);

    return (
        <div className="modalHeaderBackground relative rounded-t-lg bg-gray-950/90 bg-blend-overlay 2xl:mt-14">
            {/* <p className="2xl:text-4xl text-3xl text-center mb-5 mt-4 lg:mt-2 font-display uppercase font-bold tracking-wide hidden lg:block">
        Latest News
      </p> */}

            <div className="relative mx-auto mt-4 flex max-w-(--breakpoint-lg) flex-col border border-white/10 bg-linear-to-b from-slate-800 to-slate-800/50 pb-6 lg:rounded-xl lg:pb-0">
                <div className="vignette-sm pointer-events-none absolute inset-0 z-5 size-full object-cover opacity-50 lg:rounded-2xl"></div>
                {postID && (
                    <Link className="lg:-mb-3 mx-2 my-2.5 lg:z-20 lg:mt-4 lg:mr-0 lg:ml-4" to={-1}>
                        <Button variant="primary" className="w-32! ">
                            <div className="mr-3 flex items-center gap-x-2">
                                <ArrowLeft className="size-5" />
                                Back
                            </div>
                        </Button>
                    </Link>
                )}

                <div className="relative mx-auto flex max-w-3xl items-center justify-center sm:pb-12 lg:p-6 lg:px-0">
                    {postID ? (
                        <PostContent selectedPost={selectedPost} />
                    ) : (
                        <>
                            {/* <div className="hidden absolute top-14 bottom-0 right-full mr-7 w-px bg-slate-200 dark:bg-gray-700 sm:block"></div> */}
                            <div className="mt-4 flex flex-col space-y-12 lg:space-y-14 ">
                                {news.map((post, i) => (
                                    <div key={post.id} className="">
                                        <ChangelogCard post={post} isLast={i === news.length - 1} isFirst={i === 0} />
                                    </div>
                                ))}
                            </div>
                        </>
                    )}
                </div>

                {/* <div className="absolute top-0 left-0 w-full h-[92dvh]! bg-linear-to-b from-slate-800 to-transparent z-[-1]"></div> */}
            </div>
        </div>
    );
};

export default LatestNews;
