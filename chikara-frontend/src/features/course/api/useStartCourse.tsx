import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export interface StartCourseRequest {
    courseId: number;
}

export const useStartCourse = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.courses.start.mutationOptions({
            onSuccess: () => {
                // Invalidate user info to update active course and cash
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                // Invalidate course list to update completion status
                queryClient.invalidateQueries({
                    queryKey: api.courses.list.key(),
                });
            },
        })
    );
};
