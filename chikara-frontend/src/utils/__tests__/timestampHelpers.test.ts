import { 
    safeTimestampToNumber, 
    safeTimestampToDate, 
    hasTimestampExpired, 
    getMillisecondsUntilTimestamp 
} from '../timestampHelpers';

describe('timestampHelpers', () => {
    const now = Date.now();
    const futureTime = now + 60000; // 1 minute in the future
    const pastTime = now - 60000; // 1 minute in the past

    describe('safeTimestampToNumber', () => {
        it('should handle undefined and null values', () => {
            expect(safeTimestampToNumber(undefined)).toBe(0);
            expect(safeTimestampToNumber(null)).toBe(0);
        });

        it('should handle number values', () => {
            expect(safeTimestampToNumber(now)).toBe(now);
        });

        it('should handle string values', () => {
            expect(safeTimestampToNumber(now.toString())).toBe(now);
            expect(safeTimestampToNumber('invalid')).toBe(0);
        });

        it('should handle bigint values', () => {
            expect(safeTimestampToNumber(BigInt(now))).toBe(now);
        });
    });

    describe('safeTimestampToDate', () => {
        it('should return null for invalid timestamps', () => {
            expect(safeTimestampToDate(undefined)).toBeNull();
            expect(safeTimestampToDate(null)).toBeNull();
            expect(safeTimestampToDate(0)).toBeNull();
        });

        it('should return valid Date for valid timestamps', () => {
            const date = safeTimestampToDate(now);
            expect(date).toBeInstanceOf(Date);
            expect(date?.getTime()).toBe(now);
        });
    });

    describe('hasTimestampExpired', () => {
        it('should return false for invalid timestamps', () => {
            expect(hasTimestampExpired(undefined)).toBe(false);
            expect(hasTimestampExpired(null)).toBe(false);
            expect(hasTimestampExpired(0)).toBe(false);
        });

        it('should return true for past timestamps', () => {
            expect(hasTimestampExpired(pastTime)).toBe(true);
        });

        it('should return false for future timestamps', () => {
            expect(hasTimestampExpired(futureTime)).toBe(false);
        });
    });

    describe('getMillisecondsUntilTimestamp', () => {
        it('should return -1 for invalid timestamps', () => {
            expect(getMillisecondsUntilTimestamp(undefined)).toBe(-1);
            expect(getMillisecondsUntilTimestamp(null)).toBe(-1);
            expect(getMillisecondsUntilTimestamp(0)).toBe(-1);
        });

        it('should return positive value for future timestamps', () => {
            const result = getMillisecondsUntilTimestamp(futureTime);
            expect(result).toBeGreaterThan(0);
            expect(result).toBeLessThanOrEqual(60000);
        });

        it('should return negative value for past timestamps', () => {
            const result = getMillisecondsUntilTimestamp(pastTime);
            expect(result).toBeLessThan(0);
        });
    });
});
