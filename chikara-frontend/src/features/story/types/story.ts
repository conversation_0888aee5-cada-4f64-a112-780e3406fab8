export type ExploreNodeLocation = "shibuya" | "shinjuku" | "bunkyo" | "chiyoda" | "minato";
export type StoryEpisodeType = "NARRATIVE" | "CHOICE" | "BATTLE";

export interface StorySceneChoice {
    id: string;
    text: string;
    consequence?: string;
    nextSceneId?: number;
    // storyFlags?: string[]; // V2 Feature
    requirements?: {
        level?: number;
        // storyFlags?: string[]; // V2 Feature
    };
}

export interface StoryContent {
    scenes: StoryScene[];
    metadata?: {
        estimatedDuration?: number;
        defaultBackground?: string;
    };
}

export interface StoryScene {
    id: number;
    type: "dialogue" | "narration" | "choice" | "action" | "transition";
    speaker?: string;
    character?: string;
    text: string;
    background?: string;
    effects?: string[];
    choices?: StorySceneChoice[];
}

// Updated to match backend Record<string, {...}> structure
export type StoryEpisodeChoices = Record<
    string,
    {
        text: string;
        consequences: {
            // storyFlags?: string[]; // V2 Feature
            nextEpisodeId?: number;
        };
    }
>;

export interface StorySeasonData {
    id: number;
    name: string;
    description: string | null;
    startDate: Date;
    requiredLevel: number;
    createdAt: Date;
    updatedAt: Date;
}

export interface StoryChapterData {
    id: number;
    seasonId: number;
    name: string;
    description: string | null;
    order: number;
    unlockDate: Date;
    requiredLevel: number;
    requiredChapterIds: number[] | null;
    createdAt: Date;
    updatedAt: Date;
}

export interface StoryEpisodeData {
    id: number;
    name: string;
    description: string | null;
    episodeType: StoryEpisodeType;
    exploreLocation: ExploreNodeLocation; // Determines where story nodes are placed on the map
    content: StoryContent;
    choices: StoryEpisodeChoices | null;
    objectiveId: number; // Links to quest objective
    createdAt: Date;
    updatedAt: Date;
}
