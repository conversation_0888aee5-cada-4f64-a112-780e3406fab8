import respectImg from "@/assets/icons/UI/respect.png";
import bronzeMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Bronze.png";
import goldMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Gold.png";
import silverMedal from "@/assets/icons/leaderboards/Icon_ImageIcon_Medal_Silver.png";
import Button from "@/components/Buttons/Button";
import { DisplayGangIcon } from "@/components/DisplayGangIcon";
import LoadingState from "@/components/LoadingState";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Link } from "react-router-dom";
import ViewGangModal from "../features/gang/components/ViewGangModal";

export default function GangList() {
    const [openModal, setOpenModal] = useState(false);
    const [selectedGang, setSelectedGang] = useState(null);
    const { data: gangsList, isLoading } = useQuery(api.gang.getGangList.queryOptions());
    const { data: currentUser } = useFetchCurrentUser();

    const handleViewGang = (gang) => {
        setSelectedGang(gang);
        setOpenModal(true);
    };

    const sortedGangs = gangsList?.slice(1).sort((a, b) => b.weeklyRespect - a.weeklyRespect);

    return (
        <>
            <section className="mx-auto rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800">
                <div className="flex flex-col gap-2 p-1.5">
                    <div className="relative mb-6 flex flex-col text-center text-2xl text-custom-yellow">
                        <Link className="flex justify-start md:static" to={-1}>
                            <Button className="h-10! w-20!">Back</Button>
                            <Button className="!absolute left-0! h-10! w-20! hidden! md:block! font-display text-base">
                                Back
                            </Button>
                        </Link>

                        <p className="-mb-4 mt-2 md:my-0 font-display">Weekly Gang Leaderboard</p>
                    </div>
                    <LoadingState isLoading={isLoading}>
                        <ViewGangModal
                            hideInviteButton
                            open={openModal}
                            setOpen={setOpenModal}
                            selectedGang={selectedGang}
                            setSelectedGang={setSelectedGang}
                            currentUser={currentUser}
                        />
                        {sortedGangs?.map((gang, i) => (
                            <GangBannerSmall
                                gang={gang}
                                className="h-16"
                                keyProp={gang.id}
                                boardIndex={i}
                                onClick={() => handleViewGang(gang)}
                            />
                        ))}
                    </LoadingState>
                </div>
            </section>
        </>
    );
}

function GangBannerSmall({ gang, className, onClick, keyProp, boardIndex }) {
    if (!gang) return null;

    return (
        <div
            key={keyProp}
            className={cn(
                "relative flex gap-1 overflow-hidden rounded-lg border-2 border-black bg-linear-to-r from-blue-700 to-blue-900 p-1",
                onClick && "cursor-pointer hover:border-blue-500",
                className
            )}
            onClick={onClick ? onClick : null}
        >
            <div className="relative h-full w-10">
                {boardIndex === 0 && <img className="mx-auto mt-1.5 h-9 w-auto" src={goldMedal} alt="Gold medal" />}
                {boardIndex === 1 && <img className="mx-auto mt-1.5 h-9 w-auto" src={silverMedal} alt="Silver medal" />}
                {boardIndex === 2 && <img className="mx-auto mt-1.5 h-9 w-auto" src={bronzeMedal} alt="Bronze medal" />}
            </div>
            <div className="relative h-full w-20">
                <DisplayGangIcon src={gang} className="mx-auto h-full w-auto" />
            </div>

            <div className="flex-1">
                <p className="my-auto text-left font-body font-semibold text-custom-yellow text-stroke-sm md:text-lg">
                    {gang.name}
                </p>
                {/* <p className="text-lg text-blue-400 text-stroke-sm">
          {gang.gang_member ? gang.gang_member.length : gang.memberCount}/
          {getMaxMemberCount(gang.hideout_level)} <span className="text-sm ml-1">Members</span>
        </p> */}
                <div className="hidden h-6 items-center gap-0.5 md:flex">
                    <img
                        className="my-auto mr-1 aspect-square h-6"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nf7boKi.png`}
                        alt=""
                    />
                    <p className="mt-0.5 text-sm text-white">Lv {gang.hideout_level}</p>
                </div>
            </div>
            <div className="mr-4 flex flex-1 items-center justify-end md:mr-8">
                <img className="h-full w-auto p-3 md:p-2" src={respectImg} alt="" />
                <p className="w-10 text-lg text-red-500">{gang.weeklyRespect}</p>
            </div>
        </div>
    );
}
