import { UserModel, db } from "../lib/db.js";
import { Prisma } from "@prisma/client";

export const getMissionsByDate = async (date: Date) => {
    return await db.daily_mission
        .findMany({
            where: { missionDate: date },
            include: {
                item: true,
            },
        })
        .then((missions) =>
            missions.map((mission) => ({
                ...mission,
                itemReward: mission.item,
                item: undefined as undefined,
            }))
        );
};

export const getMissionById = async (id: number) => {
    return await db.daily_mission.findUnique({
        where: { id },
        include: {
            item: true,
        },
    });
};

export const getMissionByIdAndDate = async (id: number, date: Date) => {
    return await db.daily_mission.findFirst({
        where: {
            id,
            missionDate: date,
        },
        include: {
            item: true,
        },
    });
};

export const updateUser = async (user: UserModel) => {
    const { id, roguelikeMap, defeatedNpcs, ...userData } = user;
    return await db.user.update({
        where: { id },
        data: {
            ...userData,
            roguelikeMap: roguelikeMap ?? Prisma.DbNull,
            defeatedNpcs: defeatedNpcs ?? Prisma.DbNull,
        },
    });
};

export const updateUserMissionStart = async (user: UserModel, missionId: number, missionEndTime: number) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            currentMission: missionId,
            missionEnds: BigInt(missionEndTime),
        },
    });
};

export const updateUserMissionCancel = async (user: UserModel) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            currentMission: null,
            missionEnds: null,
        },
    });
};
