import { db } from "../lib/db.js";
import type { GangInviteTypes, Prisma } from "@prisma/client";

export const findAllGangs = async (userId: number) => {
    return await db.gang
        .findMany({
            select: {
                id: true,
                name: true,
                about: true,
                avatar: true,
                hideout_level: true,
                ownerId: true,
                user: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
                gang_member: {
                    select: {
                        id: true,
                    },
                },
                _count: {
                    select: {
                        gang_member: true,
                    },
                },
                gang_invite: {
                    where: {
                        senderId: userId,
                        inviteType: "inviteRequest",
                    },
                    select: {
                        id: true,
                    },
                },
            },
        })
        .then((gangs) =>
            gangs.map((gang) => ({
                ...gang,
                memberCount: gang._count.gang_member,
                requestSent: gang.gang_invite.length > 0,
                owner: gang.user,
                user: undefined as unknown,
            }))
        );
};

export const findGangInvites = async (gangId: number) => {
    return await db.gang_invite
        .findMany({
            where: { gangId },
            include: {
                user_gang_invite_recipientIdTouser: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
                user_gang_invite_senderIdTouser: {
                    select: {
                        id: true,
                        username: true,
                        avatar: true,
                    },
                },
            },
        })
        .then((gangs) =>
            gangs.map((gang) => ({
                ...gang,
                recipient: gang.user_gang_invite_recipientIdTouser,
                sender: gang.user_gang_invite_senderIdTouser,
                user_gang_invite_senderIdTouser: undefined as unknown,
                user_gang_invite_recipientIdTouser: undefined as unknown,
            }))
        );
};

export const findGangById = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            name: true,
            about: true,
            avatar: true,
            hideout_level: true,
            ownerId: true,
        },
    });
};

export const findUserInvites = async (userId: number) => {
    return await db.gang_invite
        .findMany({
            where: {
                recipientId: userId,
                inviteType: "invite",
            },
            include: {
                gang: {
                    select: {
                        id: true,
                        name: true,
                        about: true,
                        avatar: true,
                        hideout_level: true,
                        ownerId: true,
                        gang_member: true,
                        _count: {
                            select: {
                                gang_member: true,
                            },
                        },
                    },
                },
            },
        })
        .then((invites) =>
            invites.map((invite) => ({
                ...invite,
                gang: invite.gang
                    ? {
                          ...invite.gang,
                          memberCount: invite.gang._count.gang_member,
                      }
                    : null,
            }))
        );
};

export const findUserInviteRequests = async (userId: number) => {
    return await db.gang_invite
        .findMany({
            where: {
                senderId: userId,
                inviteType: "inviteRequest",
            },
            include: {
                gang: {
                    select: {
                        id: true,
                        name: true,
                        about: true,
                        avatar: true,
                        hideout_level: true,
                        ownerId: true,
                        gang_member: true,
                        _count: {
                            select: {
                                gang_member: true,
                            },
                        },
                    },
                },
            },
        })
        .then((requests) =>
            requests.map((request) => ({
                ...request,
                gang: request.gang
                    ? {
                          ...request.gang,
                          memberCount: request.gang._count.gang_member,
                      }
                    : null,
            }))
        );
};

export const findGangLogs = async (gangId: number) => {
    return await db.gang_log.findMany({
        where: { gangId },
        orderBy: { createdAt: "desc" },
        take: 25,
    });
};

export const findGangMembers = async (gangId: number) => {
    return await db.gang_member.findMany({
        where: { gangId },
    });
};

export const findAllGangsRaw = async () => {
    return await db.gang.findMany();
};

export const findGangMember = async (userId: number, gangId: number) => {
    return await db.gang_member.findFirst({
        where: { userId, gangId },
    });
};

export const findGangMemberWithRank = async (userId: number, gangId: number) => {
    return await db.gang_member.findFirst({
        where: { userId, gangId },
        select: { rank: true },
    });
};

export const findCurrentGangDetails = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        include: {
            gang_member: {
                include: {
                    user: {
                        select: {
                            id: true,
                            username: true,
                            avatar: true,
                            last_activity: true,
                        },
                    },
                },
            },
            chat_room: {
                select: {
                    id: true,
                    name: true,
                },
            },
        },
    });
};

export const findGangByName = async (name: string) => {
    return await db.gang.findFirst({
        where: { name },
    });
};

export const createGang = async (data: Prisma.gangCreateInput) => {
    return await db.gang.create({ data });
};

export const createGangMember = async (data: Prisma.gang_memberCreateInput) => {
    return await db.gang_member.create({ data });
};

export const findGangInvite = async (gangId: number, userId: number, type: "invite" | "inviteRequest") => {
    return await db.gang_invite.findFirst({
        where: {
            gangId,
            ...(type === "invite" ? { recipientId: userId } : { senderId: userId }),
            inviteType: type as GangInviteTypes,
        },
    });
};

export const createGangInvite = async (data: Prisma.gang_inviteCreateInput) => {
    return await db.gang_invite.create({ data });
};

export const countGangMembers = async (gangId: number) => {
    return await db.gang_member.count({
        where: { gangId },
    });
};

export const updateGangMember = async (
    data: Prisma.gang_memberUpdateInput,
    where: Prisma.gang_memberWhereUniqueInput
) => {
    return await db.gang_member.update({
        where,
        data,
    });
};

export const destroyGangMember = async (where: Prisma.gang_memberWhereUniqueInput) => {
    return await db.gang_member.delete({
        where,
    });
};

export const updateGang = async (data: Prisma.gangUpdateInput, where: Prisma.gangWhereUniqueInput) => {
    return await db.gang.update({
        where,
        data,
    });
};

export const destroyGangInvite = async (where: Prisma.gang_inviteWhereUniqueInput) => {
    return await db.gang_invite.deleteMany({
        where,
    });
};

export const createGangLog = async (data: Prisma.gang_logCreateInput) => {
    return await db.gang_log.create({ data });
};

export const findGangByPkWithResources = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
    });
};

export const findGangMemberByUserId = async (userId: number) => {
    return await db.gang_member.findFirst({
        where: { userId },
    });
};

export const updateGangResources = async (gang: { id: number }, updates: Prisma.gangUpdateInput) => {
    return await db.gang.update({
        where: { id: gang.id },
        data: updates,
    });
};

export const updateGangMemberResources = async (gangMember: { id: number }, updates: Prisma.gang_memberUpdateInput) => {
    return await db.gang_member.update({
        where: { id: gangMember.id },
        data: updates,
    });
};

export const findGangWithRespect = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            weeklyRespect: true,
            totalRespect: true,
        },
    });
};

export const findUserWithGangAndType = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            gangId: true,
            userType: true,
        },
    });
};

export const findUserWithGangId = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            gangId: true,
        },
    });
};

export const findUserWithGangAndUsername = async (userId: number) => {
    return await db.user.findUnique({
        where: { id: userId },
        select: {
            id: true,
            gangId: true,
            username: true,
        },
    });
};

export const findGangInviteByPk = async (inviteId: number) => {
    return await db.gang_invite.findUnique({
        where: { id: inviteId },
    });
};

export const updateUser = async (user: { id: number }, updates: Prisma.userUpdateInput) => {
    return await db.user.update({
        where: { id: user.id },
        data: updates,
    });
};

export const setGangOwner = async (gang: { id: number }, user: { id: number }) => {
    return await db.gang.update({
        where: { id: gang.id },
        data: { ownerId: user.id },
    });
};

export const setUserGang = async (user: { id: number }, gang: { id: number }) => {
    return await db.user.update({
        where: { id: user.id },
        data: { gangId: gang.id },
    });
};

export const destroyGang = async (gang: { id: number }) => {
    return await db.gang.delete({
        where: { id: gang.id },
    });
};

export const updateGangInvite = async (invite: { id: number }, updates: Prisma.gang_inviteUpdateInput) => {
    return await db.gang_invite.update({
        where: { id: invite.id },
        data: updates,
    });
};

export const findGangMemberWithRankAndPayoutShare = async (userId: number, gangId: number) => {
    return await db.gang_member.findFirst({
        where: { userId, gangId },
        select: {
            rank: true,
            payoutShare: true,
        },
    });
};

export const findGangWithOwner = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        include: {
            user: {
                select: {
                    id: true,
                },
            },
        },
    });
};

export const findGangWithMemberCapacity = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            hideout_level: true, // Using hideout_level as a proxy for member capacity
        },
    });
};

export const findGangWithResources = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            treasury_balance: true,
            hideout_level: true,
        },
    });
};

export const findGangMemberWithResources = async (userId: number, gangId: number) => {
    return await db.gang_member.findFirst({
        where: { userId, gangId },
        select: {
            weeklyEssence: true,
        },
    });
};

export const updateGangMemberBatch = async (
    updates: Prisma.gang_memberUpdateManyMutationInput,
    where: Prisma.gang_memberWhereInput
) => {
    return await db.gang_member.updateMany({
        where,
        data: updates,
    });
};

export const findGangWithEssence = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            essenceResource: true,
            dailyEssenceGained: true,
        },
    });
};

export const findGangMemberWithEssence = async (userId: number) => {
    return await db.gang_member.findFirst({
        where: { userId },
        select: {
            id: true,
            weeklyEssence: true,
        },
    });
};

export const findGangWithRespectAndResources = async (gangId: number) => {
    return await db.gang.findUnique({
        where: { id: gangId },
        select: {
            id: true,
            weeklyRespect: true,
            totalRespect: true,
            essenceResource: true,
            dailyEssenceGained: true,
        },
    });
};

export const findGangMemberWithEssenceAndRespect = async (userId: number) => {
    return await db.gang_member.findFirst({
        where: { userId },
        select: {
            id: true,
            weeklyEssence: true,
            weeklyRespect: true,
        },
    });
};

export const updateGangEssenceAndRespect = async (gang: { id: number }, updates: Prisma.gangUpdateInput) => {
    return await db.gang.update({
        where: { id: gang.id },
        data: updates,
    });
};

export const updateGangMemberEssenceAndRespect = async (
    gangMember: { id: number },
    updates: Prisma.gang_memberUpdateInput
) => {
    return await db.gang_member.update({
        where: { id: gangMember.id },
        data: updates,
    });
};
