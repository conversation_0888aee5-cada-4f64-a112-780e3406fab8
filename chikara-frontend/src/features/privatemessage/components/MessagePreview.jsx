import { DisplayAvatar } from "@/components/DisplayAvatar";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { clsx } from "@/lib/utils";
import { format } from "date-fns";
import { useNavigate } from "react-router-dom";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";

export default function MessagePreview({ sender, currentConvoId, currentUserId }) {
    const currentUserLastMessage = sender?.messages[0].senderId === currentUserId;

    const senderID = Number.parseInt(sender?.senderId);
    const { data: otherUserInfo, isLoading, error } = useGetUserInfo(senderID, { enabled: !!senderID });

    const navigate = useNavigate();
    const { data } = useQuery(api.messaging.getChatHistory.queryOptions());

    const allMessages = data.filter((el) => el.senderId === senderID || el.receiverId === senderID);

    const filterOutNewConversation = allMessages.filter((el) => el.message !== "<NewConversation>");

    const messagePreview = filterOutNewConversation[0].message;

    const changeConversation = (id) => {
        navigate(`/inbox/${id}`);
    };

    if (isLoading) return null;

    if (error) return "An error has occurred: " + error.message;

    const previewColour = (read, selectedSenderId) => {
        if (Number.parseInt(sender?.senderId) === Number.parseInt(selectedSenderId)) {
            return "bg-slate-800 md:bg-slate-900 brightness-[1.3]";
        } else if (read === false && !currentUserLastMessage) {
            return "bg-blue-950";
        } else {
            return "bg-slate-800 md:bg-slate-900 hover:brightness-[1.2]";
        }
    };

    return (
        <li
            key={senderID}
            className={`relative flex h-20 flex-row gap-4 px-4 py-2 ${previewColour(
                sender.messages[0].read,
                Number.parseInt(currentConvoId)
            )}`}
        >
            <DisplayAvatar className="my-auto h-12 w-auto rounded-full" src={otherUserInfo} />

            <div className="my-auto w-5/6 md:my-0">
                <div className="flex justify-between space-x-3">
                    <div className="min-w-0 flex-1">
                        <button
                            className="block focus:outline-hidden"
                            onClick={() => changeConversation(otherUserInfo?.id)}
                        >
                            <span className="absolute inset-0" aria-hidden="true" />
                            <p
                                className={clsx(
                                    "truncate text-sm text-stroke-sm",
                                    otherUserInfo.userType === "admin" ? "text-red-500" : "text-blue-500"
                                )}
                            >
                                {otherUserInfo.username}
                            </p>
                        </button>
                    </div>
                    <time
                        dateTime={sender.messages[0].createdAt}
                        className="shrink-0 whitespace-nowrap text-blue-300 text-xs"
                    >
                        {format(new Date(sender.messages[0].createdAt), "p")}
                    </time>
                </div>
                <div className="mt-1 font-medium font-body">
                    <p
                        className={clsx(
                            otherUserInfo.userType === "admin" ? "text-red-300" : "text-gray-100",
                            "line-clamp-2 text-sm"
                        )}
                    >
                        {messagePreview}
                    </p>
                </div>
            </div>
        </li>
    );
}
