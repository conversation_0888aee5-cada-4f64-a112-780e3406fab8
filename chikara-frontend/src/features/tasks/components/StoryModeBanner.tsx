import { <PERSON><PERSON><PERSON>, Star } from "lucide-react";

export default function StoryModeBanner() {
    return (
        <div className="bg-gradient-to-r from-amber-900/30 to-amber-800/20 border border-amber-700/50 rounded-lg p-3">
            <div className="flex items-center gap-3">
                <div className="flex items-center gap-2">
                    <div className="size-8 rounded-full bg-amber-900/50 border border-amber-600/50 flex items-center justify-center">
                        <BookOpen className="size-4 text-amber-300" />
                    </div>
                    <div>
                        <h3 className="text-amber-300 font-display font-medium text-sm">Main Story</h3>
                        <p className="text-amber-400/80 text-xs">Follow the academy's narrative through story quests</p>
                    </div>
                </div>
                
                <div className="ml-auto flex items-center gap-1 text-xs text-amber-400/70">
                    <Star className="size-3" />
                    <span>Auto-starting quests</span>
                </div>
            </div>
        </div>
    );
}
