import { UserModel, db } from "../lib/db.js";

export const findAllJobs = async () => {
    return await db.job.findMany();
};

export const findJobById = async (jobId: number) => {
    return await db.job.findUnique({
        where: { id: jobId },
    });
};

export const updateUserJob = async (user: UserModel, jobId: number, jobLevel: number) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobId,
            jobLevel,
        },
    });
};

export const updateUserJobLevel = async (user: UserModel, jobLevel: number) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobLevel,
        },
    });
};

export const updateUserJobPayoutTime = async (user: UserModel, payoutHour: number, blockNextPayout: boolean) => {
    return await db.user.update({
        where: { id: user.id },
        data: {
            jobPayoutHour: payoutHour,
            blockNextJobPayout: blockNextPayout,
        },
    });
};
