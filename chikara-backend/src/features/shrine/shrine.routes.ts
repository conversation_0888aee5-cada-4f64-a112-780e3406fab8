import { isLoggedInAuth, canMakeStateChangesAuth } from "../../lib/orpc.js";
import { getToday } from "../../utils/dateHelpers.js";
import { handleResponse } from "../../utils/routeHandler.js";
import * as ShrineController from "./shrine.controller.js";
import { donateToShrineSchema } from "./shrine.validation.js";

export const shrineRouter = {
    getGoal: isLoggedInAuth.handler(async () => {
        const today = getToday();
        const response = await ShrineController.getDailyShrineGoal(today);
        return handleResponse({ data: response });
    }),

    getDonations: isLoggedInAuth.handler(async () => {
        const today = getToday();
        const response = await ShrineController.getDailyShrineDonations(today);
        return handleResponse({ data: response });
    }),

    getActiveBuff: isLoggedInAuth.handler(async () => {
        return await ShrineController.isTodaysDonationGoalReached();
    }),

    donate: canMakeStateChangesAuth.input(donateToShrineSchema).handler(async ({ input, context }) => {
        const response = await ShrineController.donateToShrine(context.user.id, input.amount);
        return handleResponse(response);
    }),
};
