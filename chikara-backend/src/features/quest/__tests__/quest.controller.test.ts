import { describe, it, expect, vi, beforeEach } from "vitest";
import { QuestProgressStatus } from "@prisma/client";
import * as Quest<PERSON>ontroller from "../quest.controller.js";
import * as QuestRepository from "../../../repositories/quest.repository.js";
import * as <PERSON><PERSON><PERSON><PERSON> from "../quest.helpers.js";
import * as UserRepository from "../../../repositories/user.repository.js";

// Mock the dependencies
vi.mock("../../../repositories/quest.repository.js");
vi.mock("../../../repositories/user.repository.js");
vi.mock("../quest.helpers.js");
vi.mock("../../../core/focus.service.js");

const mockUserRepository = vi.mocked(UserRepository);
const mockQuestRepository = vi.mocked(QuestRepository);
const mockQuestHelper = vi.mocked(QuestHelper);

describe("Quest Controller", () => {
    const mockUser = {
        id: 1,
        level: 5,
        username: "testuser",
        cash: 1000,
    };

    const mockQuest = {
        id: 1,
        name: "Test Quest",
        levelReq: 3,
        disabled: false,
        requiredQuestId: null,
        quest_objective: [],
    };

    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("StartQuest", () => {
        it("should successfully start a quest when all requirements are met", async () => {
            mockQuestRepository.getQuestById.mockResolvedValue(mockQuest);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockQuestHelper.UserCanStartQuest.mockResolvedValue({ success: true });
            mockQuestRepository.createQuestProgress.mockResolvedValue({
                id: 1,
                userId: 1,
                questId: 1,
                questStatus: QuestProgressStatus.in_progress,
            });
            mockQuestRepository.createQuestObjectiveProgress.mockResolvedValue({
                id: 1,
                userId: 1,
                questObjectiveId: 1,
                count: 0,
                status: QuestProgressStatus.in_progress,
            });

            const result = await QuestController.StartQuest(1, 1);

            expect(result.data).toBeDefined();
            expect(mockQuestHelper.UserCanStartQuest).toHaveBeenCalledWith(mockUser, mockQuest);
        });

        it("should return error when quest is not found", async () => {
            mockQuestRepository.getQuestById.mockResolvedValue(null);

            const result = await QuestController.StartQuest(1, 1);

            expect(result.error).toBe("Invalid quest");
            expect(result.statusCode).toBe(400);
        });

        it("should return error when user is not found", async () => {
            mockQuestRepository.getQuestById.mockResolvedValue(mockQuest);
            mockUserRepository.getUserById.mockResolvedValue(null);

            const result = await QuestController.StartQuest(1, 1);

            expect(result.error).toBe("User not found");
            expect(result.statusCode).toBe(404);
        });

        it("should return error when requirements are not met", async () => {
            mockQuestRepository.getQuestById.mockResolvedValue(mockQuest);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockQuestHelper.UserCanStartQuest.mockResolvedValue({ error: "Requirements not met", statusCode: 400 });

            const result = await QuestController.StartQuest(1, 1);

            expect(result.error).toBe("Requirements not met");
            expect(result.statusCode).toBe(400);
        });
    });

    describe("CompleteQuest", () => {
        const mockQuestProgress = {
            id: 1,
            userId: 1,
            questId: 1,
            questStatus: QuestProgressStatus.ready_to_complete,
        };

        it("should return error when quest is disabled", async () => {
            const disabledQuest = { ...mockQuest, disabled: true };
            mockQuestRepository.getQuestById.mockResolvedValue(disabledQuest);

            const result = await QuestController.CompleteQuest(1, 1);

            expect(result.error).toBe("Quest disabled");
            expect(result.statusCode).toBe(400);
        });

        it("should return error when user level is too low", async () => {
            const lowLevelUser = { ...mockUser, level: 1 };
            const highLevelQuest = { ...mockQuest, levelReq: 10 };

            mockQuestRepository.getQuestById.mockResolvedValue(highLevelQuest);
            mockUserRepository.getUserById.mockResolvedValue(lowLevelUser);
            mockQuestHelper.checkQuestRequirements.mockResolvedValue({
                error: "User does not meet level requirement",
                statusCode: 400,
            });

            const result = await QuestController.CompleteQuest(1, 1);

            expect(result.error).toBe("User does not meet level requirement");
            expect(result.statusCode).toBe(400);
        });

        it("should return error when prerequisite quest is not completed", async () => {
            const questWithPrereq = { ...mockQuest, requiredQuestId: 2 };

            mockQuestRepository.getQuestById.mockResolvedValue(questWithPrereq);
            mockUserRepository.getUserById.mockResolvedValue(mockUser);
            mockQuestHelper.checkQuestRequirements.mockResolvedValue({
                error: "Prerequisite quest not completed",
                statusCode: 400,
            });
            mockQuestRepository.getUserQuestProgress
                .mockResolvedValueOnce(mockQuestProgress) // For the main quest
                .mockResolvedValueOnce(null); // For the prerequisite quest

            const result = await QuestController.CompleteQuest(1, 1);

            expect(result.error).toBe("Prerequisite quest not completed");
            expect(result.statusCode).toBe(400);
        });
    });
});
