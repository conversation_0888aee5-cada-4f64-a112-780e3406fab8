import type { ExploreNodeLocation, TravelMethod } from "@prisma/client";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { logPlayerAction } from "../../../lib/actionLogger.js";
import { TRAVEL_COSTS, TRAVEL_TIMES } from "../explore.constants.js";
import * as ExploreController from "../explore.controller.js";
import * as ExploreRepository from "../../../repositories/explore.repository.js";
import type { ExtUserModel } from "../../../lib/db.js";

// Mock dependencies - these are handled by setupTests.ts
vi.mock("../../../repositories/explore.repository.js");

// Mock the explore helpers module
vi.mock("../explore.helpers.js", () => ({
    validateTravelParameters: vi.fn(),
    generatePlayerNodesIfNeeded: vi.fn(),
    isNodeTypeDisabled: vi.fn(),
}));

const mockedExploreRepository = vi.mocked(ExploreRepository);
const mockedLogPlayerAction = vi.mocked(logPlayerAction);
const mockedExploreHelpers = vi.mocked(await import("../explore.helpers.js"));

describe("ExploreController - changeMapLocation", () => {
    beforeEach(() => {
        // Default behavior: validateTravelParameters doesn't throw (valid parameters)
        mockedExploreHelpers.validateTravelParameters.mockImplementation(() => {
            // Do nothing - valid parameters
        });
    });

    it("should successfully initiate travel when parameters are valid", async () => {
        // Arrange
        const userId = 1;
        const newLocation: ExploreNodeLocation = "shinjuku";
        const method: TravelMethod = "bus";
        const mockUpdatedUser = {
            cash: 1000 - TRAVEL_COSTS.bus.shinjuku,
            currentMapLocation: "shinjuku" as ExploreNodeLocation,
            travelStartTime: new Date(),
            travelEndTime: new Date(Date.now() + TRAVEL_TIMES.bus.shinjuku * 60 * 1000),
        };

        mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);
        mockedLogPlayerAction.mockResolvedValue();

        // Act
        const result = await ExploreController.changeMapLocation(userId, newLocation, method);

        // Assert
        expect(result.data).toBeDefined();
        expect(result.data?.cost).toBe(TRAVEL_COSTS.bus.shinjuku);
        expect(result.data?.travelTime).toBe(TRAVEL_TIMES.bus.shinjuku);
        expect(result.data?.method).toBe(method);
        expect(mockedExploreRepository.initiateTravel).toHaveBeenCalledWith(
            userId,
            newLocation,
            method,
            TRAVEL_COSTS.bus.shinjuku,
            TRAVEL_TIMES.bus.shinjuku
        );
        expect(mockedLogPlayerAction).toHaveBeenCalledWith(
            "EXPLORE_TRAVEL_INITIATED",
            expect.objectContaining({
                destination: newLocation,
                method,
                cost: TRAVEL_COSTS.bus.shinjuku,
                travelTimeMinutes: TRAVEL_TIMES.bus.shinjuku,
            }),
            userId
        );
    });

    it("should handle walking to all valid locations", async () => {
        // Arrange
        const userId = 1;
        const method: TravelMethod = "walk";
        const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

        for (const location of locations) {
            const mockUpdatedUser = {
                cash: 1000, // Walking is free
                currentMapLocation: location,
                travelStartTime: new Date(),
                travelEndTime: new Date(Date.now() + TRAVEL_TIMES.walk[location] * 60 * 1000),
            };

            mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);

            // Act
            const result = await ExploreController.changeMapLocation(userId, location, method);

            // Assert
            expect(result.data).toBeDefined();
            expect(result.data?.cost).toBe(0); // Walking is free
            expect(result.data?.method).toBe("walk");
            expect(result.error).toBeUndefined();
        }
    });

    it("should handle bus travel to all valid locations", async () => {
        // Arrange
        const userId = 1;
        const method: TravelMethod = "bus";
        const locations: ExploreNodeLocation[] = ["shibuya", "shinjuku", "bunkyo", "chiyoda", "minato"];

        for (const location of locations) {
            const mockUpdatedUser = {
                cash: 1000 - TRAVEL_COSTS.bus[location],
                currentMapLocation: location,
                travelStartTime: new Date(),
                travelEndTime: new Date(Date.now() + TRAVEL_TIMES.bus[location] * 60 * 1000),
            };

            mockedExploreRepository.initiateTravel.mockResolvedValue(mockUpdatedUser);

            // Act
            const result = await ExploreController.changeMapLocation(userId, location, method);

            // Assert
            expect(result.data).toBeDefined();
            expect(result.data?.cost).toBe(TRAVEL_COSTS.bus[location]);
            expect(result.data?.method).toBe("bus");
            expect(result.error).toBeUndefined();
        }
    });
});

describe("ExploreController - getExploreMapByLocation", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("should use travel status from user object when no auto-completion occurs", async () => {
        // Arrange
        const now = new Date();
        const travelEndTime = new Date(now.getTime() + 60000); // Travel ends in 1 minute
        const mockUser: ExtUserModel = {
            id: 1,
            currentMapLocation: "shibuya",
            travelStartTime: new Date(now.getTime() - 30000), // Started 30 seconds ago
            travelEndTime,
            travelMethod: "walking",
        } as ExtUserModel;

        // Mock autoCompleteTravelIfNeeded to return no completion
        mockedExploreRepository.autoCompleteTravelIfNeeded.mockResolvedValue({
            autoCompleted: false,
            completedDestination: undefined,
        });

        // Act
        const result = await ExploreController.getExploreMapByLocation(mockUser);

        // Assert
        expect(result.data).toBeDefined();
        expect(result.data?.travelStatus.isTravel).toBe(true);
        expect(result.data?.travelStatus.travelingTo).toBe("shibuya");
        expect(result.data?.travelStatus.travelMethod).toBe("walking");
        expect(result.data?.nodes).toEqual([]); // Should return empty nodes when traveling

        // Should not call getUserTravelStatus since we're using the user object
        expect(mockedExploreRepository.getUserTravelStatus).not.toHaveBeenCalled();
    });

    it("should set travel status to not traveling when auto-completion occurs", async () => {
        // Arrange
        const now = new Date();
        const travelEndTime = new Date(now.getTime() - 1000); // Travel ended 1 second ago
        const mockUser: ExtUserModel = {
            id: 1,
            currentMapLocation: "shibuya",
            travelStartTime: new Date(now.getTime() - 60000), // Started 1 minute ago
            travelEndTime,
            travelMethod: "walking",
        } as ExtUserModel;

        // Mock autoCompleteTravelIfNeeded to return completion
        mockedExploreRepository.autoCompleteTravelIfNeeded.mockResolvedValue({
            autoCompleted: true,
            completedDestination: "shibuya",
        });

        // Mock other repository functions
        mockedExploreRepository.cleanupExpiredPlayerNodes.mockResolvedValue(0);
        mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);
        mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue([]);

        // Mock the helpers
        const mockedExploreHelpers = vi.mocked(await import("../explore.helpers.js"));
        mockedExploreHelpers.generatePlayerNodesIfNeeded.mockResolvedValue();
        mockedExploreHelpers.isNodeTypeDisabled.mockReturnValue(false);

        mockedLogPlayerAction.mockResolvedValue();

        // Act
        const result = await ExploreController.getExploreMapByLocation(mockUser);

        // Assert
        expect(result.data).toBeDefined();
        expect(result.data?.travelStatus.isTravel).toBe(false);
        expect(result.data?.travelStatus.travelingTo).toBeUndefined();
        expect(result.data?.nodes).toEqual([]); // Empty since no nodes were mocked

        // Should not call getUserTravelStatus since we're using the computed status
        expect(mockedExploreRepository.getUserTravelStatus).not.toHaveBeenCalled();

        // Should log the travel completion
        expect(mockedLogPlayerAction).toHaveBeenCalledWith(
            "EXPLORE_TRAVEL_COMPLETED",
            expect.objectContaining({
                newLocation: "shibuya",
                autoCompleted: true,
            }),
            1
        );
    });

    it("should use travel status from user object when user is not traveling", async () => {
        // Arrange
        const mockUser: ExtUserModel = {
            id: 1,
            currentMapLocation: "shibuya",
            travelStartTime: null,
            travelEndTime: null,
            travelMethod: null,
        } as ExtUserModel;

        // Mock autoCompleteTravelIfNeeded to return no completion
        mockedExploreRepository.autoCompleteTravelIfNeeded.mockResolvedValue({
            autoCompleted: false,
            completedDestination: undefined,
        });

        // Mock other repository functions
        mockedExploreRepository.cleanupExpiredPlayerNodes.mockResolvedValue(0);
        mockedExploreRepository.getStaticNodesByLocation.mockResolvedValue([]);
        mockedExploreRepository.getActivePlayerNodesByLocation.mockResolvedValue([]);

        // Mock the helpers
        const mockedExploreHelpers = vi.mocked(await import("../explore.helpers.js"));
        mockedExploreHelpers.generatePlayerNodesIfNeeded.mockResolvedValue();
        mockedExploreHelpers.isNodeTypeDisabled.mockReturnValue(false);

        // Act
        const result = await ExploreController.getExploreMapByLocation(mockUser);

        // Assert
        expect(result.data).toBeDefined();
        expect(result.data?.travelStatus.isTravel).toBe(false);
        expect(result.data?.travelStatus.travelingTo).toBeUndefined();
        expect(result.data?.travelStatus.travelMethod).toBeUndefined();

        // Should not call getUserTravelStatus since we're using the user object
        expect(mockedExploreRepository.getUserTravelStatus).not.toHaveBeenCalled();
    });
});
