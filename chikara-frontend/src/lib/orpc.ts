import { createORPC<PERSON>lient } from "@orpc/client";
import { RPCLink } from "@orpc/client/fetch";
import { createTanstackQueryUtils } from "@orpc/tanstack-query";
import type { AppRouterClient } from "../../../chikara-backend/src/routes";
import type { AdminRouterClient } from "../../../chikara-backend/src/admin.routes";
import { SimpleCsrfProtectionLinkPlugin } from "@orpc/client/plugins";
import { ForbiddenError, UnauthorizedError } from "@/helpers/apiError";

const baseURL = import.meta.env.VITE_API_BASE_URL || "http://localhost:3000";

const handleFetch = async (url: Request, options: RequestInit) => {
    const response = await fetch(url, {
        ...options,
        credentials: "include",
    });

    if (response.status === 401) {
        throw new UnauthorizedError();
    }

    if (response.status === 403) {
        throw new ForbiddenError();
    }

    return response;
};

export const link = new RPCLink({
    url: `${baseURL}/rpc`,
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
    async fetch(url, options) {
        const response = await handleFetch(url, options);
        return response;
    },
    plugins: [new SimpleCsrfProtectionLinkPlugin()],
});

export const client: AppRouterClient = createORPCClient(link);
export const orpc = createTanstackQueryUtils(client);

// Admin Endpoints
export const adminLink = new RPCLink({
    url: `${baseURL}/admin-rpc`,
    headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
    },
    async fetch(url, options) {
        const response = await handleFetch(url, options);
        return response;
    },
    plugins: [new SimpleCsrfProtectionLinkPlugin()],
});

export const adminClient: AdminRouterClient = createORPCClient(adminLink);
export const adminOrpc = createTanstackQueryUtils(adminClient);

export interface QueryOptions {
    staleTime?: number;
    enabled?: boolean;
    // [key: string]: unknown;
}

export type { AppRouterClient, AdminRouterClient };
