import QuestItem from "@/features/tasks/components/QuestItem";
import { getQuestStatus } from "@/features/tasks/hooks/useQuestSortAndFilter";
import type { InventoryItem } from "@/types/item";
import type { QuestWithProgress, QuestGiver } from "@/types/quest";
import React, { type Dispatch, type SetStateAction } from "react";

interface QuestAccordionProps {
    quests: QuestWithProgress[];
    questGivers: QuestGiver[];
    availableQuests?: QuestWithProgress[];
    inventory?: InventoryItem[];
    expandedQuests: number[];
    setExpandedQuests: Dispatch<SetStateAction<number[]>>;
    showStatusGroups?: boolean;
    isPinned?: boolean;
    title?: string;
    titleIcon?: React.ReactNode;
}

export default function QuestAccordion({
    quests,
    questGivers,
    availableQuests = [],
    inventory = [],
    expandedQuests,
    setExpandedQuests,
    showStatusGroups = true,
    isPinned = false,
    title,
    titleIcon,
}: QuestAccordionProps) {
    if (quests.length === 0) {
        return null;
    }

    const renderQuestItem = (quest: QuestWithProgress) => (
        <QuestItem
            key={quest.id}
            quest={quest}
            questGivers={questGivers}
            availableQuests={availableQuests}
            inventory={inventory}
            expandedQuests={expandedQuests}
            setExpandedQuests={setExpandedQuests}
            isPinned={isPinned}
        />
    );

    if (!showStatusGroups) {
        // Simple list without status grouping (for pinned quests)
        return (
            <div className="space-y-2">
                {title && (
                    <div className="text-sm font-medium uppercase tracking-wider px-2 text-amber-400 flex items-center gap-2">
                        {titleIcon}
                        {title}
                    </div>
                )}
                <div className="space-y-2">{quests.map(renderQuestItem)}</div>
            </div>
        );
    }

    // Grouped by status
    return (
        <div className="space-y-2">
            {["ready_to_complete", "in_progress", "available", "complete"].map((statusGroup) => {
                const questsInGroup = quests.filter((q) => getQuestStatus(q) === statusGroup);

                if (questsInGroup.length === 0) return null;

                return (
                    <div key={statusGroup} className="mb-4">
                        {/* Status group header */}
                        <div className="text-sm font-medium uppercase tracking-wider mt-6 mb-2 px-2 text-gray-400">
                            {statusGroup === "ready_to_complete" && "Ready to Complete"}
                            {statusGroup === "in_progress" && "In Progress"}
                            {statusGroup === "available" && "Available"}
                            {statusGroup === "complete" && "Completed"}
                        </div>

                        {/* Quests in this status group */}
                        <div className="space-y-2">{questsInGroup.map(renderQuestItem)}</div>
                    </div>
                );
            })}
        </div>
    );
}
