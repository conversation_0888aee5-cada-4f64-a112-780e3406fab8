import { db } from "../lib/db.js";
import { QuestObjectiveTypes } from "../types/quest.js";
import { getTomorrow } from "../utils/dateHelpers.js";
import { DropChanceTypes, Prisma, QuestProgressStatus, QuestTargetAction } from "@prisma/client";

export const findDailyQuestsByUserId = async (userId: number, questDate: Date) => {
    return await db.daily_quest.findMany({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
        },
        include: {
            item: true,
        },
    });
};

export const findDailyQuestById = async (questId: number) => {
    return await db.daily_quest.findUnique({
        where: { id: questId },
    });
};

export const updateDailyQuest = async (id: number, updateData: Prisma.daily_questUpdateInput) => {
    return await db.daily_quest.update({
        where: { id },
        data: updateData,
    });
};

export const countCompletedDailyQuests = async (userId: number, questDate: Date) => {
    return await db.daily_quest.count({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
            questStatus: QuestProgressStatus.complete,
        },
    });
};

export const findSimilarLevelUser = async (userId: number, combatLevel: number, level: number, range = 5) => {
    return await db.user.findFirst({
        where: {
            combatLevel: {
                lte: combatLevel,
            },
            level: {
                gte: level - range,
                lte: level + range,
            },
            id: {
                not: userId,
            },
            userType: {
                not: "admin",
            },
        },
    });
};

export const findPotentialDrops = async (minLevel: number, maxLevel: number) => {
    return await db.drop_chance.findMany({
        where: {
            dropChanceType: DropChanceTypes.roguelike,
            location: "any",
            minLevel: {
                lte: minLevel,
            },
            maxLevel: {
                gte: maxLevel,
            },
            dropRate: {
                gt: 0,
            },
        },
        include: {
            item: true,
        },
        orderBy: {
            dropRate: "asc",
        },
    });
};

export const destroyOldDailyQuests = async (userId: number, today: Date) => {
    return await db.daily_quest.deleteMany({
        where: {
            userId: userId,
            createdAt: {
                lt: today,
            },
        },
    });
};

export const countDailyQuests = async (userId: number, questDate: Date) => {
    return await db.daily_quest.count({
        where: {
            userId: userId,
            createdAt: {
                gte: questDate,
                lt: getTomorrow(),
            },
        },
    });
};

export const createDailyQuest = async (questData: Prisma.daily_questCreateInput) => {
    return await db.daily_quest.create({
        data: questData,
    });
};

interface DailyQuestAttributes {
    userId: number;
    objectiveType: QuestObjectiveTypes;
    questStatus: QuestProgressStatus;
    target?: number;
    targetAction?: QuestTargetAction | null;
    createdAt: {
        gte: Date;
        lt: Date;
    };
}

export const findDailyQuestProgress = async (
    userId: number,
    objectiveType: QuestObjectiveTypes,
    today: Date,
    target: number | null = null,
    targetAction: QuestTargetAction | string | null = null
) => {
    const questFilter: DailyQuestAttributes = {
        userId,
        objectiveType,
        createdAt: {
            gte: today,
            lt: getTomorrow(),
        },
        questStatus: QuestProgressStatus.in_progress,
    };
    if (target) {
        questFilter.target = target;
    }
    if (targetAction) {
        questFilter.targetAction = targetAction as QuestTargetAction;
    }

    return await db.daily_quest.findFirst({
        where: questFilter,
    });
};

export const findDailyQuestByIdWithRewards = async (questId: number) => {
    return await db.daily_quest.findUnique({
        where: { id: questId },
    });
};
