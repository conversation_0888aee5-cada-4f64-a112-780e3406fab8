import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

export interface CompleteEpisodeRequest {
    episodeId: number;
    choices?: Record<string, string>;
}

/**
 * Hook to complete an episode
 */
export const useCompleteEpisode = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.story.completeEpisode.mutationOptions({
            onSuccess: () => {
                // Also invalidate quests as story completion may affect quest progress
                queryClient.invalidateQueries({ queryKey: api.quests.getAvailable.key() });
                // Invalidate explore map to refresh story nodes
                queryClient.invalidateQueries({ queryKey: api.explore.getMapByLocation.key() });
            },
        })
    );
};
