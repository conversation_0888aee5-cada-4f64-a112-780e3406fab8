import { Button } from "@/components/ui/button";
import { useGetHousingList } from "../api/useGetHousingList";
import { usePurchaseProperty } from "../api/usePropertyMutations";
import { Property } from "../types/property";
import { Home, DollarSign } from "lucide-react";
import Spinner from "@/components/Spinners/Spinner";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";

interface PropertyCardProps {
    property: Property;
    userCash: number;
    onPurchase: (_propertyId: number) => void;
    isPurchasing: boolean;
}

const PropertyCard = ({ property, userCash, onPurchase, isPurchasing }: PropertyCardProps) => {
    const canAfford = userCash >= property.cost;

    return (
        <div className="bg-slate-800/50 border border-slate-700/50 rounded-xl p-6 backdrop-blur-sm">
            <div className="flex items-start gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-500/20">
                    <Home className="h-6 w-6 text-indigo-400" />
                </div>
                <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-1">{property.name}</h3>
                    <p className="text-sm text-slate-300 mb-3">{property.description}</p>

                    <div className="flex items-center gap-4 mb-4">
                        <div className="flex items-center gap-1 text-green-400">
                            <DollarSign className="h-4 w-4" />
                            <span className="font-medium">{property.cost.toLocaleString()}</span>
                        </div>
                        <div className="text-slate-400 text-sm">{property.slots} slots</div>
                    </div>

                    <Button
                        disabled={!canAfford || isPurchasing}
                        className={`w-full ${
                            canAfford ? "bg-indigo-600 hover:bg-indigo-700" : "bg-slate-600 cursor-not-allowed"
                        }`}
                        onClick={() => onPurchase(property.id)}
                    >
                        {isPurchasing ? <Spinner /> : canAfford ? "Purchase" : "Insufficient Funds"}
                    </Button>
                </div>
            </div>
        </div>
    );
};

export const PropertyList = () => {
    const { data: properties, isLoading, error } = useGetHousingList();
    const { data: user } = useFetchCurrentUser();
    const purchaseProperty = usePurchaseProperty();

    const handlePurchase = (propertyId: number) => {
        purchaseProperty.mutate({ propertyId });
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center py-8">
                <Spinner />
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-8">
                <p className="text-red-400">Failed to load properties</p>
            </div>
        );
    }

    if (!properties || properties.length === 0) {
        return (
            <div className="text-center py-8">
                <p className="text-slate-400">No properties available</p>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            <h2 className="text-xl font-semibold text-white mb-4">Available Properties</h2>
            <div className="grid gap-4">
                {properties.map((property) => (
                    <PropertyCard
                        key={property.id}
                        property={property}
                        userCash={user?.cash || 0}
                        isPurchasing={purchaseProperty.isPending}
                        onPurchase={handlePurchase}
                    />
                ))}
            </div>
        </div>
    );
};
