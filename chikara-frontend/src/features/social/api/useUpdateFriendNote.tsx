import { Friend } from "@/features/social/types/social";
import { api } from "@/helpers/api";
import { UseMutationOptions, useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";

interface UpdateFriendNoteParams {
    friendId: number;
    note: string | null;
}

const useUpdateFriendNote = (options: Partial<UseMutationOptions<Friend, Error, UpdateFriendNoteParams>> = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.social.updateFriendNote.mutationOptions({
            onSuccess: () => {
                toast.success("Friend note updated!");
                // Invalidate friends list to refresh the data
                queryClient.invalidateQueries({ queryKey: api.social.getFriends.key() });
            },
            onError: (error) => {
                toast.error(error.message || "Failed to update friend note");
            },
            ...options,
        })
    );
};

export default useUpdateFriendNote;
