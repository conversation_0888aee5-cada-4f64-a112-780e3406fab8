import Button from "@/components/Buttons/Button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { cn } from "@/lib/utils";
import { Clock, Coins, MapPin, Train, User, Wallet } from "lucide-react";
import type { TravelMethod, TravelMethodOption } from "../types/explore.types";
import { formatCurrency } from "@/utils/currencyHelpers";

interface TravelMethodModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSelectMethod: (_method: TravelMethod) => void;
    destination: {
        id: string;
        name: string;
        cost: number;
        travelTimes: {
            walk: number;
            bus: number;
        };
    };
    userCash: number;
    isLoading?: boolean;
}

export const TravelMethodModal = ({
    isOpen,
    onClose,
    onSelectMethod,
    destination,
    userCash,
    isLoading = false,
}: TravelMethodModalProps) => {
    const travelMethods: TravelMethodOption[] = [
        {
            method: "walk",
            name: "Walk",
            description: "Free travel, but takes longer",
            cost: 0,
            time: destination.travelTimes.walk,
            icon: "👟",
        },
        {
            method: "bus",
            name: "Bus",
            description: "Faster travel, but costs money",
            cost: destination.cost,
            time: destination.travelTimes.bus,
            icon: "🚌",
        },
    ];

    const handleMethodSelect = (method: TravelMethod) => {
        onSelectMethod(method);
        onClose();
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-md bg-gray-800/95 border-purple-900/30 text-white">
                <DialogHeader className="space-y-3">
                    <DialogTitle className="flex items-center gap-3 text-xl font-semibold">
                        <div className="size-8 rounded-full bg-blue-500/20 flex items-center justify-center">
                            <MapPin className="size-4 text-blue-400" />
                        </div>
                        Travel to {destination.name}
                    </DialogTitle>
                    <DialogDescription className="text-gray-400">
                        Choose your preferred method of travel to {destination.name}.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-3 mt-4">
                    {travelMethods.map((method) => {
                        const canAfford = userCash >= method.cost;
                        const isDisabled = !canAfford || isLoading;
                        const isWalk = method.method === "walk";

                        return (
                            <div
                                key={method.method}
                                className={cn(
                                    "bg-gray-900/50 rounded-lg p-4 border transition-all duration-200 relative",
                                    canAfford
                                        ? "border-gray-700 hover:border-purple-500 hover:bg-gray-800/70"
                                        : "border-red-800/50 bg-red-900/10"
                                )}
                            >
                                <div className="flex items-start gap-4">
                                    {/* Icon */}
                                    <div
                                        className={cn(
                                            "size-12 rounded-full flex items-center justify-center text-xl flex-shrink-0",
                                            isWalk
                                                ? "bg-green-500/20 border border-green-500/30"
                                                : "bg-blue-500/20 border border-blue-500/30"
                                        )}
                                    >
                                        {method.icon}
                                    </div>

                                    {/* Content */}
                                    <div className="flex-1 min-w-0">
                                        <div className="flex items-center justify-between mb-2">
                                            <h3 className="font-semibold text-white text-lg">{method.name}</h3>
                                            {!canAfford && method.cost > 0 && (
                                                <span className="text-xs text-red-400 bg-red-900/30 px-2 py-1 rounded-full">
                                                    Cannot Afford
                                                </span>
                                            )}
                                        </div>

                                        <p className="text-sm text-gray-400 mb-3">{method.description}</p>

                                        {/* Stats */}
                                        <div className="grid grid-cols-2 gap-3 mb-4">
                                            <div className="flex items-center gap-2">
                                                <div className="size-6 rounded-full bg-gray-700 flex items-center justify-center">
                                                    <Clock className="size-3 text-blue-400" />
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-400">Time</p>
                                                    <p className="text-sm text-white font-medium">{method.time} min</p>
                                                </div>
                                            </div>
                                            <div className="flex items-center gap-2">
                                                <div className="size-6 rounded-full bg-gray-700 flex items-center justify-center">
                                                    <Coins className="size-3 text-yellow-400" />
                                                </div>
                                                <div>
                                                    <p className="text-xs text-gray-400">Cost</p>
                                                    <p className="text-sm text-white font-medium">
                                                        {method.cost === 0 ? "Free" : formatCurrency(method.cost)}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>

                                        {!canAfford && method.cost > 0 && (
                                            <div className="mb-3 text-xs text-red-400 bg-red-900/20 p-2 rounded border border-red-800/30">
                                                Insufficient funds (need {formatCurrency(method.cost - userCash)} more)
                                            </div>
                                        )}

                                        {/* Action Button */}
                                        <Button
                                            disabled={isDisabled}
                                            size="sm"
                                            className={cn(
                                                "w-full transition-all duration-200",
                                                !canAfford &&
                                                    "!bg-gray-700 hover:!bg-gray-700 !text-gray-400 !border-gray-600"
                                            )}
                                            onClick={() => handleMethodSelect(method.method)}
                                        >
                                            {isLoading ? (
                                                <div className="flex items-center gap-2">
                                                    <div className="size-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                                                    <span>Starting Travel...</span>
                                                </div>
                                            ) : (
                                                <div className="flex items-center gap-2 -ml-5">
                                                    {isWalk ? (
                                                        <User className="size-4" />
                                                    ) : (
                                                        <Train className="size-4" />
                                                    )}
                                                    <span>{canAfford ? `${method.name}` : "Cannot Afford"}</span>
                                                </div>
                                            )}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>

                {/* User Cash Display */}
                <div className="mt-4 bg-gray-800/50 rounded-lg p-3 border border-purple-900/30">
                    <div className="flex items-center gap-3">
                        <div className="size-8 rounded-full bg-yellow-500/20 flex items-center justify-center">
                            <Wallet className="size-4 text-yellow-400" />
                        </div>
                        <div>
                            <p className="text-xs text-gray-400">Your Cash</p>
                            <p className="text-sm font-medium text-white">{formatCurrency(userCash)}</p>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};
