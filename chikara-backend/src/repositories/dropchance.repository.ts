import { DropChanceModel, db } from "../lib/db.js";
import type { Prisma } from "@prisma/client";
import { DropChanceTypes } from "@prisma/client";

export const findAllDrops = async () => {
    return await db.drop_chance.findMany({
        include: {
            item: {
                select: {
                    id: true,
                    name: true,
                    itemType: true,
                    rarity: true,
                    level: true,
                    about: true,
                    cashValue: true,
                    image: true,
                    damage: true,
                    armour: true,
                    health: true,
                    energy: true,
                    actionPoints: true,
                    baseAmmo: true,
                    itemEffects: true,
                    recipeUnlockId: true,
                },
            },
        },
    });
};

export const findDropChanceById = async (id: number) => {
    return await db.drop_chance.findUnique({
        where: { id },
    });
};

export const createDropChance = async (data: Partial<DropChanceModel>) => {
    return await db.drop_chance.create({
        data: data as Prisma.drop_chanceCreateInput,
    });
};

export const updateDropChance = async (dropChance: DropChanceModel, data: Partial<DropChanceModel>) => {
    // Update the dropChance with the new data
    return await db.drop_chance.update({
        where: { id: dropChance.id },
        data: data as Prisma.drop_chanceUpdateInput,
    });
};

export const deleteDropChanceById = async (id: number) => {
    return await db.drop_chance.delete({
        where: { id },
    });
};

// export const findDropChanceByItemAndCreature = async (itemId: number, creatureId: number) => {
//     // Since creatureId is not in the Prisma schema, we need to adapt this query
//     // For now, we'll find by itemId and filter in memory, but you should update your schema
//     const drops = await db.drop_chance.findMany({
//         where: {
//             itemId,
//         },
//     });

//     // Filter by creatureId (this is a temporary solution)
//     // In a real implementation, you should update your Prisma schema to include creatureId
//     return drops.find((drop) => drop.creatureId === creatureId) || null;
// };

export const getAllDropChanceTypes = () => {
    return Object.values(DropChanceTypes);
};
