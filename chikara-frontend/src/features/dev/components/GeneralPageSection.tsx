import Button from "@/components/Buttons/Button";
import { api } from "@/helpers/api";
import { handleLogout } from "@/helpers/handleLogout";
import { handlePost } from "@/helpers/axiosInstance";
import { useQueryClient, useMutation } from "@tanstack/react-query";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "../../../app/store/stores";
import toast from "react-hot-toast";

const GeneralPageSection = () => {
    const queryClient = useQueryClient();
    const navigate = useNavigate();
    const setAuthed = useAuthStore((state) => state.setAuthed);

    // Mutation hooks for dev operations
    const addXpMutation = useMutation(
        api.dev.addXp.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        }),
                    100
                );
                toast.success("XP added successfully!");
            },
            onError: (error) => {
                console.error("Error adding XP:", error);
                toast.error(error.message || "Failed to add XP");
            },
        })
    );

    const addCashMutation = useMutation(
        api.dev.addCash.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        }),
                    100
                );
                toast.success("Cash added successfully!");
            },
            onError: (error) => {
                console.error("Error adding cash:", error);
                toast.error(error.message || "Failed to add cash");
            },
        })
    );

    const addStatsMutation = useMutation(
        api.dev.addStats.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        }),
                    100
                );
                toast.success("Stats added successfully!");
            },
            onError: (error) => {
                console.error("Error adding stats:", error);
                toast.error(error.message || "Failed to add stats");
            },
        })
    );

    const removeStatsMutation = useMutation(
        api.dev.removeStats.mutationOptions({
            onSuccess: () => {
                setTimeout(
                    () =>
                        queryClient.invalidateQueries({
                            queryKey: api.user.getCurrentUserInfo.key(),
                        }),
                    100
                );
                toast.success("Stats removed successfully!");
            },
            onError: (error) => {
                console.error("Error removing stats:", error);
                toast.error(error.message || "Failed to remove stats");
            },
        })
    );

    const resetQuestsMutation = useMutation(
        api.dev.resetQuests.mutationOptions({
            onSuccess: () => {
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.getCombinedList.key() }), 100);
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.getAvailable.key() }), 100);
                toast.success("Quests reset successfully!");
            },
            onError: (error) => {
                console.error("Error resetting quests:", error);
                toast.error(error.message || "Failed to reset quests");
            },
        })
    );

    // Handler functions
    const addXP = () => {
        addXpMutation.mutate({ xp: 2000 });
    };

    const addCash = () => {
        addCashMutation.mutate({});
    };

    const addStats = () => {
        addStatsMutation.mutate({});
    };

    const removeStats = () => {
        removeStatsMutation.mutate({});
    };

    const resetQuests = () => {
        resetQuestsMutation.mutate({});
    };

    const completeQuestsMutation = useMutation(
        api.dev.completeQuests.mutationOptions({
            onSuccess: () => {
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.getCombinedList.key() }), 100);
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.quests.getAvailable.key() }), 100);
                toast.success("All quests completed successfully!");
            },
            onError: (error) => {
                console.error("Error completing quests:", error);
                toast.error(error.message || "Failed to complete quests");
            },
        })
    );

    const completeQuests = () => {
        completeQuestsMutation.mutate({});
    };

    const createTestUserMutation = useMutation(
        api.admin.createTestUser.mutationOptions({
            onSuccess: async (response) => {
                await handleLogout();
                setTimeout(() => {
                    login(response.email);
                }, 500);
                toast.success("Test user created successfully!");
            },
            onError: (error) => {
                console.error("Error creating test user:", error);
                toast.error(error.message || "Failed to create test user");
            },
        })
    );

    const randomRoguelikeMutation = useMutation(
        api.dev.randomRoguelike.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                setTimeout(() => queryClient.invalidateQueries({ queryKey: api.roguelike.getCurrentMap.key() }), 100);
                navigate("/streets");
                toast.success("Random map generated successfully!");
            },
            onError: (error) => {
                console.error("Error generating random map:", error);
                toast.error(error.message || "Failed to generate random map");
            },
        })
    );

    const addAllItemsMutation = useMutation(
        api.dev.addAllItems.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.user.getInventory.key() });
                toast.success("All items added successfully!");
            },
            onError: (error) => {
                console.error("Error adding all items:", error);
                toast.error(error.message || "Failed to add all items");
            },
        })
    );

    const login = async (email: string) => {
        try {
            await handlePost(api.auth.login, {
                email: email,
                password: "testtest123",
            });

            setAuthed(true);
            navigate("/home");
        } catch (error) {
            console.error("Login error:", error);
        }
    };

    const createTestUser = () => {
        createTestUserMutation.mutate({});
    };

    const randomMap = () => {
        randomRoguelikeMutation.mutate({});
    };

    const allItems = () => {
        addAllItemsMutation.mutate({});
    };

    const deleteExploreNodesMutation = useMutation(
        api.dev.deleteExploreNodes.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.explore.getMapByLocation.key() });
                toast.success("Explore nodes reset successfully!");
            },
            onError: (error) => {
                console.error("Error resetting explore nodes:", error);
                toast.error(error.message || "Failed to reset explore nodes");
            },
        })
    );

    const resetExploreNodes = () => {
        deleteExploreNodesMutation.mutate({});
    };

    return (
        <div className="grid grid-cols-2 gap-3 p-2">
            <Button className="text-sm!" variant="primary" onClick={addXP}>
                Gain 2000 EXP
            </Button>
            <Button className="text-sm!" variant="primary" onClick={addCash}>
                +5k Cash
            </Button>
            <Button className="text-sm!" variant="primary" onClick={addStats}>
                +200 Stats
            </Button>
            <Button className="text-sm!" variant="primary" onClick={removeStats}>
                -200 Stats
            </Button>
            <Button className="text-sm!" variant="primary" onClick={resetQuests}>
                Reset Quests
            </Button>
            <Button className="text-sm!" variant="primary" onClick={randomMap}>
                Random Map
            </Button>
            <Button className="text-sm!" variant="primary" onClick={allItems}>
                Add All Items
            </Button>
            <Button className="text-sm!" variant="primary" onClick={completeQuests}>
                Complete Quests
            </Button>
            <Button className="text-sm!" variant="primary" onClick={createTestUser}>
                Create Test User
            </Button>
            <Button className="text-sm!" variant="primary" onClick={resetExploreNodes}>
                Reset Explore Nodes
            </Button>
        </div>
    );
};

export default GeneralPageSection;
