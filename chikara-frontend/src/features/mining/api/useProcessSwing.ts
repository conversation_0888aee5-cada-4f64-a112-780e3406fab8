import { useMutation, useQueryClient } from "@tanstack/react-query";
import { api } from "@/helpers/api";

const useProcessSwing = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.mining.processSwing.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.mining.getMiningSession.key() });
            },
        })
    );
};

export default useProcessSwing;
