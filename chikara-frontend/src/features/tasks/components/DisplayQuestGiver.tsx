import defaultAvatar from "@/assets/images/defaultAvatar.png";
import { cn } from "@/lib/utils";
import { Image } from "@unpic/react";
import type { QuestGiver } from "@/types/quest";

const CDN_URL = import.meta.env.VITE_IMAGE_CDN_URL || import.meta.env.VITE_API_BASE_URL;

interface DisplayQuestGiverProps {
    src: QuestGiver;
    className?: string;
    width?: number;
    height?: number;
}

const avatarURL = (questGiver: QuestGiver) => {
    if (questGiver?.avatar) {
        if (questGiver?.avatar.startsWith("http") || questGiver?.avatar.startsWith("https")) {
            return questGiver.avatar;
        }
        return `${CDN_URL}${questGiver.avatar}`;
    }

    return defaultAvatar;
};

export const DisplayQuestGiver = ({ src, className, width = 100, height = 100, ...props }: DisplayQuestGiverProps) => {
    const imageSrc = src ? avatarURL(src) : defaultAvatar;

    return (
        <Image
            src={imageSrc}
            className={cn(className ? className : "rounded-md object-cover")}
            alt={`${src?.name}'s avatar`}
            width={width}
            height={height}
            layout="constrained"
            onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                (e.target as HTMLImageElement).onerror = null;
                (e.target as HTMLImageElement).src = defaultAvatar;
            }}
            {...props}
        />
    );
};
