import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBankDeposit = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.bank.deposit.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.bank.getBankTransactions.key(),
                });
            },
        })
    );
};
