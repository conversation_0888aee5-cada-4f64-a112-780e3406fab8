import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { CraftingRecipe } from "../types/crafting";

type CraftingRecipeResponse = CraftingRecipe[];

/**
 * Custom hook to fetch crafting recipes using API
 */
const useGetCraftingRecipes = (options: { staleTime?: number; enabled?: boolean } = {}) => {
    return useQuery(
        api.crafting.getRecipes.queryOptions({
            ...options,
        })
    );
};

export default useGetCraftingRecipes;
