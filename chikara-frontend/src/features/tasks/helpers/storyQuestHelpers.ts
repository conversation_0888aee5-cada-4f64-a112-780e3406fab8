import type { QuestWithProgress } from "@/types/quest";

/**
 * Get the visual styling for story quest borders
 */
export const getStoryQuestBorderStyle = (quest: QuestWithProgress, status: string) => {
    if (!quest.isStoryQuest) return "";

    switch (status) {
        case "available":
            return "border-amber-600/70 bg-gradient-to-r from-amber-900/20 to-amber-800/10";
        case "in_progress":
            return "border-amber-500/70 bg-gradient-to-r from-amber-900/30 to-amber-800/15";
        case "ready_to_complete":
            return "border-amber-400/80 bg-gradient-to-r from-amber-900/40 to-amber-800/20 shadow-amber-500/20 shadow-lg";
        case "complete":
            return "border-amber-700/50 bg-gradient-to-r from-amber-900/15 to-amber-800/5";
        default:
            return "border-amber-600/50";
    }
};

/**
 * Get the chapter display text for a story quest
 */
export const getChapterDisplayText = (quest: QuestWithProgress): string | null => {
    if (!quest.isStoryQuest || !quest.story_chapter) return null;

    const season = quest.story_chapter.story_season;
    const chapter = quest.story_chapter;

    if (season) {
        return `${season.name} - Chapter ${chapter.order}: ${chapter.name}`;
    }

    return `Chapter ${chapter.order}: ${chapter.name}`;
};

/**
 * Get the story quest icon based on status
 */
export const getStoryQuestIcon = (quest: QuestWithProgress, status: string): string => {
    if (!quest.isStoryQuest) return "";

    switch (status) {
        case "available":
            return "📖"; // Book icon for available story
        case "in_progress":
            return "⭐"; // Star icon for active story
        case "ready_to_complete":
            return "✨"; // Sparkles for ready to complete
        case "complete":
            return "📚"; // Closed book for completed
        default:
            return "📖";
    }
};
