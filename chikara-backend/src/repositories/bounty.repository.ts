import { BountyModel, db } from "../lib/db.js";

export async function findAllBounties(): Promise<BountyModel[]> {
    return await db.bounty.findMany();
}

export async function findActiveBountiesWithUsers(): Promise<BountyModel[]> {
    return await db.bounty.findMany({
        where: { active: true },
        include: {
            placer: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
            target: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
    });
}

export async function findActiveBountyByTargetId(targetId: number): Promise<BountyModel | null> {
    return await db.bounty.findFirst({
        where: {
            AND: [{ targetId: targetId }, { active: true }],
        },
    });
}

export async function createBounty(data: {
    amount: number;
    reason: string;
    placerId: number;
    targetId: number;
}): Promise<BountyModel> {
    return await db.bounty.create({
        data,
    });
}

export async function updateBountyAmount(bountyId: number, newAmount: number): Promise<BountyModel> {
    return await db.bounty.update({
        where: { id: bountyId },
        data: { amount: newAmount },
    });
}

export async function updateBountyClaimStatus(
    bountyId: number,
    claimedById: number,
    active: boolean
): Promise<BountyModel> {
    return await db.bounty.update({
        where: { id: bountyId },
        data: {
            active,
            claimedById,
        },
    });
}

export async function deleteBountyById(id: number): Promise<void> {
    await db.bounty.delete({
        where: { id },
    });
}

export async function getUserTypeAndLevel(userId: number) {
    const user = await db.user.findUnique({
        where: { id: userId },
        select: {
            userType: true,
            level: true,
        },
    });
    if (!user) return null;
    return user;
}
