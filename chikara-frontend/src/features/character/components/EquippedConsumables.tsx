// Not currently implemented - ignore this file

import { DisplayItem } from "@/components/DisplayItem";
import useGetInventory from "@/hooks/api/useGetInventory";
import { useState } from "react";

export default function EquippedConsumables({
    mobile = false,
    isBattle = false,
}: {
    mobile?: boolean;
    isBattle?: boolean;
}) {
    return null;
    // const [activeSlot, setActiveSlot] = useState<number | null>(null);

    // const { data: equippedConsumables = [], isLoading } = useGetEquippedConsumables();
    // const { data: inventory = [] } = useGetInventory();

    // const clearActiveSlot = () => setActiveSlot(null);

    // const equipMutation = useEquipConsumable(clearActiveSlot);
    // const unequipMutation = useUnequipConsumable(clearActiveSlot);

    // if (isLoading) return <div className="text-center">Loading consumables...</div>;

    // const consumableSlots = [1, 2, 3];
    // const availableConsumables = inventory.filter((item) => item.item?.itemType === "refillable_consumable");

    // return (
    //     <div className={`p-2 ${mobile ? "mx-auto w-full" : "w-full"}`}>
    //         <h3 className="mb-2 text-lg font-semibold dark:text-white">Combat Consumables</h3>
    //         <div className="flex space-x-2 mb-4">
    //             {consumableSlots.map((slot) => {
    //                 const consumable = equippedConsumables.find((c) => c.slot === slot);
    //                 return (
    //                     <div
    //                         key={slot}
    //                         className={`${mobile ? "size-20" : "size-24"} border-2 rounded-lg flex items-center justify-center cursor-pointer relative ${activeSlot === slot ? "border-indigo-500" : "border-gray-700"}`}
    //                         onClick={() => setActiveSlot(activeSlot === slot ? null : slot)}
    //                     >
    //                         {consumable ? (
    //                             <>
    //                                 <DisplayItem item={consumable.item} className="size-full object-contain p-1" />
    //                                 <span className="absolute bottom-2 right-2 text-white text-base z-20 bg-black/70 px-1 rounded-sm">
    //                                     {consumable.count}
    //                                 </span>
    //                             </>
    //                         ) : (
    //                             <span className="text-gray-400">Slot {slot}</span>
    //                         )}
    //                     </div>
    //                 );
    //             })}
    //         </div>

    //         {activeSlot && (
    //             <div className="mt-2 p-2 bg-gray-800 rounded-lg">
    //                 <div className="flex justify-between mb-2">
    //                     <h4 className="text-white">Slot {activeSlot}</h4>
    //                     {equippedConsumables.find((c) => c.slot === activeSlot) && (
    //                         <button
    //                             className="px-2 py-1 text-xs bg-red-600 text-white rounded-sm hover:bg-red-700"
    //                             disabled={unequipMutation.isPending}
    //                             onClick={() => unequipMutation.mutate(String(activeSlot))}
    //                         >
    //                             Unequip
    //                         </button>
    //                     )}
    //                 </div>

    //                 <div className="grid grid-cols-3 gap-2 mt-2">
    //                     {availableConsumables.length > 0 ? (
    //                         availableConsumables.map((item) => (
    //                             <div
    //                                 key={item.id}
    //                                 className="border border-gray-700 rounded-sm p-1 cursor-pointer hover:bg-gray-700 transition-colors"
    //                                 onClick={() =>
    //                                     equipMutation.mutate({ userItemId: item.id, slot: String(activeSlot) })
    //                                 }
    //                             >
    //                                 <div className="flex flex-col items-center">
    //                                     <DisplayItem item={item.item} className="size-10 object-contain" />
    //                                     <span className="text-xs text-white truncate w-full text-center">
    //                                         {item.item.name}
    //                                     </span>
    //                                     <span className="text-xs text-gray-400">x{item.count}</span>
    //                                 </div>
    //                             </div>
    //                         ))
    //                     ) : (
    //                         <div className="col-span-3 text-center text-gray-400">
    //                             No refillable consumables in inventory
    //                         </div>
    //                     )}
    //                 </div>
    //             </div>
    //         )}
    //     </div>
    // );
}
