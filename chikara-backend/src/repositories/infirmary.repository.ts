import { db } from "../lib/db.js";

export const findAllHospitalizedUsers = async () => {
    return await db.user.findMany({
        where: {
            hospitalisedUntil: {
                not: null,
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            hospitalisedUntil: true,
            hospitalisedReason: true,
            user_status_effect: {
                select: {
                    id: true,
                    stacks: true,
                    customName: true,
                    effect: {
                        select: {
                            name: true,
                            effectType: true,
                            tier: true,
                        },
                    },
                },
            },
        },
    });
};

export const findAllInjuredUsers = async (userId: number) => {
    return await db.user.findMany({
        where: {
            hospitalisedUntil: null,
            id: {
                not: userId,
            },
            // Ensure we only get users that have at least one status effect
            user_status_effect: {
                some: {},
            },
        },
        select: {
            id: true,
            username: true,
            avatar: true,
            hospitalisedUntil: true,
            hospitalisedReason: true,
            gangId: true,
            user_status_effect: {
                select: {
                    id: true,
                    stacks: true,
                    endsAt: true,
                    customName: true,
                    effect: {
                        select: {
                            name: true,
                            category: true,
                            tier: true,
                            effectType: true,
                        },
                    },
                },
            },
        },
    });
};

export const findUserStatusEffects = async (userId: number) => {
    return await db.user_status_effect.findMany({
        where: { userId },
        include: {
            effect: true,
        },
    });
};

export const findUserStatusEffectById = async (id: number) => {
    return await db.user_status_effect.findUnique({
        where: { id },
        include: {
            effect: true,
        },
    });
};
