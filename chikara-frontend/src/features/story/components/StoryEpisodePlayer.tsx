import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft, ArrowRight, SkipForward, BookOpen, CheckCircle } from "lucide-react";
import { useCompleteEpisode } from "../api/useCompleteEpisode";
import { StoryContent, StoryEpisodeData } from "../types/story";

export const StoryEpisodePlayer: React.FC<{ episodeData: StoryEpisodeData; onClose: () => void }> = ({
    episodeData,
    onClose,
}) => {
    const navigate = useNavigate();
    const completeEpisodeMutation = useCompleteEpisode();

    const [currentSceneIndex, setCurrentSceneIndex] = useState(0);

    const episode = episodeData;
    const content = episode?.content as StoryContent;
    const scenes = content?.scenes || [];
    const currentScene = scenes[currentSceneIndex];

    const handleCompleteEpisode = async () => {
        try {
            if (!episode?.id) {
                console.error("No episode ID available");
                return;
            }

            await completeEpisodeMutation.mutateAsync({
                episodeId: episode.id,
                choices: {},
            });

            // Close the episode player - this will trigger explore map refresh
            // and quest progress updates
            onClose();
        } catch (error) {
            console.error("Failed to complete episode:", error);
        }
    };

    const handleNextScene = () => {
        if (currentSceneIndex < scenes.length - 1) {
            setCurrentSceneIndex(currentSceneIndex + 1);
        } else {
            // Episode completed
            handleCompleteEpisode();
        }
    };

    const handlePreviousScene = () => {
        if (currentSceneIndex > 0) {
            setCurrentSceneIndex(currentSceneIndex - 1);
        }
    };

    const handleSkipToEnd = () => {
        setCurrentSceneIndex(scenes.length - 1);
    };

    if (!episode || !content) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <h2 className="text-2xl font-bold mb-4">Episode Not Found</h2>
                    <Button onClick={() => navigate("/explore")}>
                        <ArrowLeft className="h-4 w-4 mr-2" />
                        Back to Explore
                    </Button>
                </div>
            </div>
        );
    }

    const progressPercentage = ((currentSceneIndex + 1) / scenes.length) * 100;

    return (
        <div className="min-h-screen bg-gradient-to-b from-slate-900 to-slate-800 text-white">
            {/* Header */}
            <div className="sticky top-0 z-10 bg-slate-900/90 backdrop-blur-sm border-b border-slate-700">
                <div className="container mx-auto px-4 py-3">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:bg-slate-700"
                                onClick={() => navigate("/explore")}
                            >
                                <ArrowLeft className="h-4 w-4 mr-2" />
                                Back
                            </Button>
                            <div>
                                <h1 className="text-lg font-semibold">{episode.name}</h1>
                                <Badge variant="secondary" className="text-xs">
                                    {episode.episodeType}
                                </Badge>
                            </div>
                        </div>

                        <div className="flex items-center gap-2">
                            <Button
                                variant="ghost"
                                size="sm"
                                className="text-white hover:bg-slate-700"
                                onClick={handleSkipToEnd}
                            >
                                <SkipForward className="h-4 w-4" />
                            </Button>
                        </div>
                    </div>

                    {/* Progress Bar */}
                    <div className="mt-3">
                        <div className="flex justify-between text-sm text-slate-400 mb-1">
                            <span>
                                Scene {currentSceneIndex + 1} of {scenes.length}
                            </span>
                            <span>{Math.round(progressPercentage)}%</span>
                        </div>
                        <Progress value={progressPercentage} className="h-2" />
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-4xl mx-auto">
                    {currentScene && (
                        <Card className="bg-slate-800/50 border-slate-600 backdrop-blur-sm">
                            <CardContent className="p-8">
                                {/* Scene Content */}
                                <div className="space-y-6">
                                    {/* Character/Speaker */}
                                    {currentScene.speaker && (
                                        <div className="flex items-center gap-3 mb-4">
                                            <div className="w-12 h-12 rounded-full bg-primary/20 flex items-center justify-center">
                                                <span className="text-lg font-bold">
                                                    {currentScene.speaker.charAt(0)}
                                                </span>
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-lg">{currentScene.speaker}</h3>
                                                {currentScene.character && (
                                                    <p className="text-sm text-slate-400">{currentScene.character}</p>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Scene Text */}
                                    <div className="prose prose-invert max-w-none">
                                        <p className="text-lg leading-relaxed">{currentScene.text}</p>
                                    </div>

                                    {/* Scene Effects */}
                                    {currentScene.effects && currentScene.effects.length > 0 && (
                                        <div className="flex flex-wrap gap-2">
                                            {currentScene.effects.map((effect, index) => (
                                                <Badge key={index} variant="outline" className="text-xs">
                                                    {effect}
                                                </Badge>
                                            ))}
                                        </div>
                                    )}

                                    {/* Navigation */}
                                    {currentScene.type !== "choice" && (
                                        <div className="flex justify-between items-center mt-8 pt-6 border-t border-slate-600">
                                            <Button
                                                variant="ghost"
                                                disabled={currentSceneIndex === 0}
                                                className="text-white hover:bg-slate-700"
                                                onClick={handlePreviousScene}
                                            >
                                                <ArrowLeft className="h-4 w-4 mr-2" />
                                                Previous
                                            </Button>

                                            <Button
                                                className="bg-primary hover:bg-primary/90"
                                                disabled={completeEpisodeMutation.isPending}
                                                onClick={handleNextScene}
                                            >
                                                {currentSceneIndex === scenes.length - 1 ? (
                                                    <>
                                                        {completeEpisodeMutation.isPending ? (
                                                            <>
                                                                Completing...
                                                                <BookOpen className="h-4 w-4 ml-2 animate-pulse" />
                                                            </>
                                                        ) : (
                                                            <>
                                                                Complete Episode
                                                                <CheckCircle className="h-4 w-4 ml-2" />
                                                            </>
                                                        )}
                                                    </>
                                                ) : (
                                                    <>
                                                        Next
                                                        <ArrowRight className="h-4 w-4 ml-2" />
                                                    </>
                                                )}
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>
                    )}
                </div>
            </div>
        </div>
    );
};
