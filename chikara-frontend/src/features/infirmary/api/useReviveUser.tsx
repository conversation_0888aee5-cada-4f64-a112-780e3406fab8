import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export interface ReviveUserRequest {
    targetId: number;
}

/**
 * Hook to revive another user (requires revive talent)
 */
export const useReviveUser = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.infirmary.revivePlayer.mutationOptions({
            onSuccess: () => {
                toast.success("Revived user successfully!");
                queryClient.invalidateQueries({
                    queryKey: api.infirmary.getHospitalList.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.infirmary.getInjuredList.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );
};

export default useReviveUser;
