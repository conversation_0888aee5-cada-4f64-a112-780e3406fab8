import { useQuery } from "@tanstack/react-query";
import { api, QueryOptions } from "@/helpers/api";

/**
 * Hook for fetching active bounties with more control
 */
export const useActiveBounties = (options?: QueryOptions) => {
    return useQuery(
        api.bounties.getActiveBountyList.queryOptions({
            staleTime: options?.staleTime || 30000,
            enabled: options?.enabled !== false,
        })
    );
};
