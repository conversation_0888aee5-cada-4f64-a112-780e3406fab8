import type { TalentInfo, UnlockedTalents } from "@/features/talents/types/talents";
import useGetUnlockedTalents from "@/features/talents/api/useGetUnlockedTalents";
import { useMemo } from "react";

function useIsTalentUnlocked(talentName: string) {
    const { data: unlockedTalents } = useGetUnlockedTalents({
        staleTime: Number.POSITIVE_INFINITY,
    });

    const talentDetails = useMemo(() => {
        const talent = unlockedTalents?.talentList?.find((t) => t.talentInfo.name === talentName);

        if (!talent) return null;

        return {
            talentId: talent.talentId,
            level: talent.level,
            modifier: talent.talentInfo[`tier${talent.level}Modifier` as keyof TalentInfo] as number | null,
            secondaryModifier: talent.talentInfo.secondaryModifier,
        };
    }, [unlockedTalents, talentName]);

    return talentDetails;
}

export default useIsTalentUnlocked;
