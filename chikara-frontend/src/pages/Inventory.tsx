import EquipTooltip from "@/features/character/components/EquipTooltip";
import Equipped from "@/features/character/components/Equipped";
// import EquippedConsumables from "@/features/character/components/EquippedConsumables";
import InventoryTable from "@/features/character/components/InventoryTable";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import useCheckMobileScreen from "@/hooks/useCheckMobileScreen";
import { useQuery } from "@tanstack/react-query";
import { useLayoutEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { api } from "@/helpers/api";
import { cn } from "@/lib/utils";

function Inventory() {
    const [currentPage, setCurrentPage] = useState("/inventory");
    const [equipTooltipFilter, setEquipTooltipFilter] = useState("head");
    const width = useCheckMobileScreen(true);
    const location = useLocation();
    const navigate = useNavigate();

    useLayoutEffect(() => {
        if (location.pathname && location.pathname !== currentPage) {
            setCurrentPage(location.pathname);
        }
    }, [location.pathname]);

    const { data: currentUser, error, isLoading } = useFetchCurrentUser();
    const { data } = useQuery(api.user.getInventory.queryOptions());
    const { data: equippedItems } = useQuery(api.user.getEquippedItems.queryOptions());

    if (isLoading) return null;
    if (error) return "An error has occurred: " + error.message;

    const weaponOptions = ["weapon", "offhand", "ranged"];
    const sortItemBy = weaponOptions.includes(equipTooltipFilter) ? "damage" : "armour";

    const secondFilter = equipTooltipFilter === "shield" ? "offhand" : null;

    const equipTooltipItems =
        data
            ?.filter((el) => el.item?.itemType === equipTooltipFilter || el.item?.itemType === secondFilter)
            ?.sort((a, b) => {
                if (a.item[sortItemBy] > b.item[sortItemBy]) return -1;
                if (a.item[sortItemBy] < b.item[sortItemBy]) return 1;
                return 0;
            }) || [];

    if (equippedItems && equippedItems.shield) {
        equippedItems.offhand = equippedItems.shield;
    }

    const tabs = [
        {
            name: "/inventory",
            text: "Items",
            current: location.pathname === "/inventory",
        },
        {
            name: "/equipment",
            text: "Equipment",
            current: location.pathname === "/equipment",
        },
    ];

    return (
        <div className="flex h-full flex-col gap-2 md:mx-auto md:max-w-208 2xl:max-w-(--breakpoint-xl) 2xl:flex-row">
            <div>
                <EquipTooltip
                    inventory={equipTooltipItems}
                    currentUser={currentUser}
                    equippedItems={equippedItems || []}
                />
                <div className="block 2xl:hidden">
                    <nav className="relative z-0 flex divide-x divide-gray-700 shadow-sm" aria-label="Tabs">
                        {tabs.map((tab, tabIdx) => (
                            <a
                                key={tab.name}
                                aria-current={tab.current ? "page" : undefined}
                                className={cn(
                                    tab.current ? "text-gray-900" : "text-gray-500 hover:text-gray-700",
                                    tabIdx === tabs.length - 1 ? "md:rounded-r-lg" : "",
                                    tabIdx === 0 ? "md:rounded-l-lg" : "",
                                    "group relative min-w-0 flex-1 cursor-pointer overflow-hidden bg-white p-4 text-center font-medium text-sm hover:bg-gray-50 focus:z-10 dark:bg-gray-800 dark:text-slate-200"
                                )}
                                onClick={() => navigate(tab.name)}
                            >
                                <span>{tab.text}</span>
                                <span
                                    aria-hidden="true"
                                    className={cn(
                                        tab.current ? "bg-indigo-500" : "bg-transparent",
                                        "absolute inset-x-0 bottom-0 h-0.5"
                                    )}
                                />
                            </a>
                        ))}
                    </nav>
                </div>
            </div>
            {typeof width === "number" && width <= 1536 ? (
                currentPage === "/inventory" ? (
                    <InventoryTable currentUser={currentUser} equippedItems={equippedItems} />
                ) : (
                    <div className="flex flex-col gap-4">
                        <Equipped mobile equippedItems={equippedItems} setEquipTooltipFilter={setEquipTooltipFilter} />
                        {/* <EquippedConsumables mobile /> */}
                    </div>
                )
            ) : (
                <div className="flex w-full gap-6">
                    <div className="2xl:mt-0 2xl:w-[75%]">
                        <InventoryTable currentUser={currentUser} equippedItems={equippedItems} />
                    </div>
                    <div className="2xl:w-[30%]">
                        <Equipped equippedItems={equippedItems} setEquipTooltipFilter={setEquipTooltipFilter} />
                        {/* <div className="mt-6">
                            <EquippedConsumables />
                        </div> */}
                    </div>
                </div>
            )}
        </div>
    );
}

export default Inventory;
