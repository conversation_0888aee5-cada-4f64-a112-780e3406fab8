import { cn } from "@/lib/utils";
import { CircleCheck } from "lucide-react";

interface NotificationBadgeProps {
    amount?: number;
    right?: string | null;
    top?: boolean;
    disabled?: boolean;
    empty?: boolean;
    pulse?: boolean;
    square?: boolean;
    tick?: boolean;
    notifSize?: "lg" | "sm";
    className?: string;
}

export default function NotificationBadge({
    amount,
    right = null,
    top = false,
    disabled = false,
    empty = false,
    pulse = false,
    square = false,
    tick = false,
    notifSize = "lg",
    className,
}: NotificationBadgeProps) {
    if (disabled) return null;

    if (tick) return <TickBadge className={className} />;

    if (empty) return <div className={cn("z-40 rounded-full bg-red-500", pulse && "animate-pulse", className)}></div>;

    if (!amount) return null;

    return (
        <div
            className={cn(
                className,
                "z-40 inline-flex items-center justify-center border-2 border-white bg-red-500 font-body font-semibold text-stroke-sm text-white text-xs dark:border-gray-900",
                square ? "rounded-md" : "absolute rounded-full",
                right ? right : "-end-2",
                top ? "-top-2" : "-bottom-2",
                notifSize === "sm" ? "top-0 size-5" : "size-6",
                pulse && "animate-pulse"
            )}
        >
            {amount}
        </div>
    );
}

const TickBadge = ({ className }: { className?: string }) => {
    return <CircleCheck className={cn("z-40 rounded-full bg-black p-[0.5px] text-green-500", className)} />;
};
