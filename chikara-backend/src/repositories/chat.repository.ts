import { db } from "../lib/db.js";
import type { Prisma } from "@prisma/client";

export const findChatMessages = async (roomId: number, limit: number) => {
    return await db.chat_message.findMany({
        where: { chatRoomId: roomId },
        orderBy: { createdAt: "desc" },
        take: limit,
        include: {
            chat_message: {
                include: {
                    user: {
                        select: {
                            id: true,
                            avatar: true,
                            username: true,
                        },
                    },
                },
            },
            user: {
                select: {
                    id: true,
                    avatar: true,
                    username: true,
                    createdAt: true,
                    userType: true,
                    level: true,
                },
            },
        },
    });
};

export const findAllChatRooms = async () => {
    return await db.chat_room.findMany();
};

export const findMessageWithUser = async (messageId: number) => {
    return await db.chat_message.findUnique({
        where: {
            id: messageId,
        },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    avatar: true,
                },
            },
        },
    });
};

export const createChatMessageWithUser = async (data: Prisma.chat_messageCreateInput) => {
    return await db.chat_message.create({
        data,
    });
};

export const countEmoteUsage = async (emote: string) => {
    return await db.chat_message.count({
        where: {
            chatRoomId: 1,
            message: {
                contains: `:${emote}`,
            },
        },
    });
};
