import { z } from "zod";
import { NotificationTypes } from "../../types/notification.js";

// Notification endpoint validation
export const sendNotificationSchema = z.object({
    type: z.nativeEnum(NotificationTypes),
    details: z.string().min(1, "Details are required"),
});

// XP manipulation validation
export const addXpSchema = z.object({
    xp: z.number().int().positive("XP must be a positive integer"),
});

// Cash manipulation validation
export const addCashSchema = z.object({
    cash: z.number().int().positive("Cash must be a positive integer").optional().default(5000),
});

// Stats manipulation validation
export const addStatsSchema = z.object({
    amount: z.number().int().positive("Amount must be a positive integer").optional().default(200),
});

export const removeStatsSchema = z.object({
    amount: z.number().int().positive("Amount must be a positive integer").optional().default(200),
});

// Item manipulation validation
export const addItemSchema = z.object({
    itemId: z.number().int().positive("Item ID must be a positive integer"),
    quantity: z.number().int().positive("Quantity must be a positive integer").optional().default(1),
});

// Effects manipulation validation
export const removeAllEffectsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});

export const addRandomEffectsSchema = z.object({
    userId: z.number().int().positive("User ID must be a positive integer").optional(),
});

// Pet XP validation
export const addPetXpSchema = z.object({
    xp: z.number().int().positive("XP must be a positive integer").optional().default(100),
});

// Roguelike validation
export const randomRoguelikeSchema = z.object({
    level: z.number().int().positive("Level must be a positive integer").optional(),
    location: z.string().optional(),
});

// Export all schemas as a single object for consistency with other validation files
const devSchema = {
    sendNotification: sendNotificationSchema,
    addXp: addXpSchema,
    addCash: addCashSchema,
    addStats: addStatsSchema,
    removeStats: removeStatsSchema,
    addItem: addItemSchema,
    removeAllEffects: removeAllEffectsSchema,
    addRandomEffects: addRandomEffectsSchema,
    addPetXp: addPetXpSchema,
    randomRoguelike: randomRoguelikeSchema,
};

export default devSchema;
