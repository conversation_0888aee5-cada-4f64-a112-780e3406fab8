import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

interface UseCustomizePetOptions {
    onSuccess?: (data: any) => void;
    onError?: (error: Error) => void;
}

const useCustomizePet = (options: UseCustomizePetOptions = {}) => {
    const queryClient = useQueryClient();

    return useMutation(
        api.pets.customize.mutationOptions({
            onSuccess: (data) => {
                // Invalidate pets list to refresh data
                queryClient.invalidateQueries({
                    queryKey: api.pets.list.key(),
                });
                options.onSuccess?.(data);
            },
            onError: (error) => {
                console.error("Customize pet error:", error);
                options.onError?.(error);
            },
        })
    );
};

export default useCustomizePet;
