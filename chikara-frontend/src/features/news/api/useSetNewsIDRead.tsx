import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const useSetNewsIDRead = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.user.setLastNewsIDRead.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error: Error) => {
                console.log(error?.message || "An error occurred");
                //   toast.error(`Error: ${error.message}`);
            },
        })
    );

    return {
        setNewsID: mutation,
    };
};

export default useSetNewsIDRead;
