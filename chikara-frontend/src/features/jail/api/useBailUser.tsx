import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";

export interface BailUserRequest {
    targetId: number;
}

/**
 * Hook to bail a user out of jail
 */
export const useBailUser = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.jail.bail.mutationOptions({
            onSuccess: () => {
                toast.success("Bailed out user successfully!");
                queryClient.invalidateQueries({
                    queryKey: api.jail.jailList.key(),
                });

                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error) => {
                toast.error(error?.message || "An error occurred");
            },
        })
    );
};

export default useBailUser;
