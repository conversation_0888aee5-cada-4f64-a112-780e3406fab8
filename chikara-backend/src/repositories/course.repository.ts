import { CourseRewardType } from "../data/courses.js";
import { db } from "../lib/db.js";
import { getNow } from "../utils/dateHelpers.js";
import { SkillType } from "@prisma/client";

export const createCompletedCourse = async (userId: number, courseId: number) => {
    return await db.user_completed_course.create({
        data: {
            userId,
            courseId,
            completedAt: getNow(),
        },
    });
};

const resetUserActiveCourse = async (userId: number) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            activeCourseId: null,
            courseEnds: null,
        },
    });
};

export const updateUserAfterCourseCompletion = async (
    userId: number,
    rewardType: CourseRewardType,
    stat?: SkillType,
    amount?: number,
    recipeId?: number
) => {
    // Different updates based on reward type
    if (rewardType === CourseRewardType.STAT && stat && amount) {
        // First reset the active course
        await resetUserActiveCourse(userId);

        // Check if the stat is a combat stat that should use the skill system
        const combatStats = ["strength", "defence", "intelligence", "dexterity", "endurance", "vitality"];

        if (combatStats.includes(stat)) {
            // Update the skill level through the skill system
            await db.user_skill.upsert({
                where: {
                    userId_skillType: {
                        userId,
                        skillType: stat,
                    },
                },
                update: {
                    level: { increment: amount },
                    updatedAt: new Date(),
                },
                create: {
                    userId,
                    skillType: stat,
                    level: 1 + amount,
                    experience: 0,
                    talentPoints: 0,
                },
            });
        } else {
            // For non-combat stats, update the user table directly
            await db.user.update({
                where: { id: userId },
                data: {
                    [stat]: { increment: amount },
                },
            });
        }

        // Return the updated user
        return await db.user.findUnique({
            where: { id: userId },
        });
    } else if (rewardType === CourseRewardType.TALENT_POINTS && amount) {
        // Reset the active course first
        await resetUserActiveCourse(userId);

        // Then update talent points
        return await db.user.update({
            where: { id: userId },
            data: {
                talentPoints: { increment: amount },
            },
        });
    } else if (rewardType === CourseRewardType.CRAFTING_RECIPE && recipeId) {
        // First reset the active course
        await resetUserActiveCourse(userId);

        // Then create the recipe connection
        await db.user_recipe.upsert({
            where: {
                craftingRecipeId_userId: {
                    craftingRecipeId: recipeId,
                    userId: userId,
                },
            },
            update: {},
            create: {
                craftingRecipeId: recipeId,
                userId: userId,
            },
        });

        // Return the updated user
        return await db.user.findUnique({
            where: { id: userId },
        });
    } else {
        // Just reset the course if no valid reward type
        return await resetUserActiveCourse(userId);
    }
};

export const findCompletedCourses = async (userId: number) => {
    return await db.user_completed_course.findMany({
        where: {
            userId,
        },
        select: {
            courseId: true,
        },
    });
};

export const findCompletedCourseByUserAndCourse = async (userId: number, courseId: number) => {
    return await db.user_completed_course.findFirst({
        where: {
            userId,
            courseId,
        },
    });
};

export const updateUserOnCourseStart = async (
    userId: number,
    courseId: number,
    courseCost: number,
    courseEndTime: number
) => {
    return await db.user.update({
        where: { id: userId },
        data: {
            cash: { decrement: courseCost },
            activeCourseId: courseId,
            courseEnds: BigInt(courseEndTime),
        },
    });
};
