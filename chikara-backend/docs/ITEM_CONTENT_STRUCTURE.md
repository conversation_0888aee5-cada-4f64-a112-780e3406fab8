# Item Content Structure Guide

This guide details how to structure item content for the game, based on the `schema.prisma` file.

## `item` Model

The `item` model is the core of all items in the game.

| Field            | Type           | Description                                                                                 |
| ---------------- | -------------- | ------------------------------------------------------------------------------------------- |
| `id`             | `Int`          | Unique identifier for the item.                                                             |
| `name`           | `String`       | The name of the item.                                                                       |
| `itemType`       | `ItemTypes`    | The type of the item. See [ItemTypes Enum](#itemtypes-enum).                                |
| `rarity`         | `ItemRarities` | The rarity of the item. See [ItemRarities Enum](#itemrarities-enum).                        |
| `level`          | `Int`          | The level requirement for the item.                                                         |
| `about`          | `String?`      | A short description of the item.                                                            |
| `cashValue`      | `Int?`         | The value of the item in cash.                                                              |
| `image`          | `String?`      | The image URL for the item.                                                                 |
| `damage`         | `Int?`         | The damage value for weapons.                                                               |
| `armour`         | `Int?`         | The armour value for wearable items.                                                        |
| `health`         | `Int?`         | The health bonus provided by the item.                                                      |
| `energy`         | `Int?`         | The energy bonus provided by the item.                                                      |
| `actionPoints`   | `Int?`         | The action points bonus provided by the item.                                               |
| `baseAmmo`       | `Int?`         | The base ammo for ranged weapons.                                                           |
| `itemEffects`    | `Json?`        | A JSON field for special item effects. See [itemEffects Structure](#itemeffects-structure). |
| `createdAt`      | `DateTime`     | The timestamp when the item was created.                                                    |
| `updatedAt`      | `DateTime`     | The timestamp when the item was last updated.                                               |
| `recipeUnlockId` | `Int?`         | The ID of the crafting recipe this item unlocks.                                            |
| `petUnlockId`    | `Int?`         | The ID of the pet this item unlocks.                                                        |

---

## Enums

### `ItemTypes` Enum

| Value        | Description                        |
| ------------ | ---------------------------------- |
| `weapon`     | Melee weapons.                     |
| `ranged`     | Ranged weapons.                    |
| `head`       | Headwear.                          |
| `chest`      | Chest armour.                      |
| `hands`      | Gloves.                            |
| `legs`       | Leg armour.                        |
| `feet`       | Footwear.                          |
| `finger`     | Rings.                             |
| `offhand`    | Off-hand items.                    |
| `shield`     | Shields.                           |
| `consumable` | Items that can be used up.         |
| `crafting`   | Materials for crafting.            |
| `junk`       | Items with no practical use.       |
| `quest`      | Items related to quests.           |
| `special`    | Special items.                     |
| `recipe`     | Crafting recipes.                  |
| `upgrade`    | Items used to upgrade other items. |
| `pet`        | Pet-related items.                 |
| `pet_food`   | Food for pets.                     |

### `ItemRarities` Enum

| Value        | Description            |
| ------------ | ---------------------- |
| `novice`     | The lowest rarity.     |
| `standard`   | Standard rarity.       |
| `enhanced`   | Enhanced rarity.       |
| `specialist` | Specialist rarity.     |
| `military`   | Military-grade rarity. |
| `legendary`  | The highest rarity.    |

---

## `itemEffects` Structure

The `itemEffects` field is a JSON array of objects, where each object represents a special effect of the item.

### Standard `ItemEffect` Object

Based on `chikara-backend/src/types/item.ts`:

| Key              | Type                                                     | Description                                             |
| ---------------- | -------------------------------------------------------- | ------------------------------------------------------- |
| `effectKey`      | `string`                                                 | The key for the effect (e.g., "lifesteal", "strength"). |
| `effectModifier` | `"multiply" \| "add" \| "subtract" \| "divide" \| "set"` | How the effect value is applied.                        |
| `effectValue`    | `number?`                                                | The numerical value of the effect.                      |
| `effectTier`     | `string?`                                                | The tier of the effect.                                 |
| `effectGroup`    | `string?`                                                | The group of the effect (e.g., "treatment").            |
| `description`    | `string?`                                                | A user-facing description of the effect.                |

**Example:**

```json
[
    {
        "effectKey": "strength",
        "effectModifier": "add",
        "effectValue": 10,
        "description": "Increases Strength by 10."
    }
]
```

---

## Item Integration

Items are deeply integrated into various game systems. Here's how they connect with other models in `schema.prisma`:

### Player Inventories (`user_item`)

When a player acquires an item, it is stored in the `user_item` table. This table links a `user` to an `item` and includes details like `count`, `upgradeLevel`, and `quality`.

### Equipment (`equipped_item`)

Players can equip items of certain types (e.g., `weapon`, `head`, `chest`). Equipped items are tracked in the `equipped_item` table, which links a `user_item` to an equipment `slot`.

### Shops (`shop_listing`)

Items can be sold in shops. The `shop_listing` table defines which items are available in which `shop`, and can specify a `customCost`, `repRequired`, `stock`, and `currency`.

### Crafting (`crafting_recipe` and `recipe_item`)

Items are central to crafting.

- **`crafting_recipe`**: An item of type `recipe` can unlock a new crafting recipe for a player. The `recipeUnlockId` on the `item` model links to a `crafting_recipe`.
- **`recipe_item`**: This table defines the ingredients (`input`) and results (`output`) for a `crafting_recipe`.

### Quests and Missions

Items can be objectives or rewards for quests and missions.

- **`quest_objective`**: A quest can require the player to acquire a specific `item`.
- **`quest_reward`**: An `item` can be a reward for completing a `quest`.
- **`daily_mission`**: An `item` can be a reward for a `daily_mission`.
- **`daily_quest`**: An `item` can be a reward for a `daily_quest`.

### Drops (`drop_chance`)

Items can be dropped as loot from various activities. The `drop_chance` table defines the probability of an `item` dropping from different sources like `creature`, `boss`, `scavenge`, etc.

### Auctions (`auction_item`)

Players can sell items to other players through the auction house. The `auction_item` table tracks listed items, their prices, and status.

### Pets (`pet`)

An item can be used to unlock a pet. The `petUnlockId` on the `item` model links to a `pet`. Items with the `pet_food` type can be used to feed pets.
