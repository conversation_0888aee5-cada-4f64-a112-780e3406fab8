import Button from "@/components/Buttons/Button";
import LoadingState from "@/components/LoadingState";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { Link } from "react-router-dom";
import GangBanner from "../features/gang/components/GangBanner";
import ViewGangModal from "../features/gang/components/ViewGangModal";

export default function GangList() {
    const [openModal, setOpenModal] = useState(false);
    const [selectedGang, setSelectedGang] = useState(null);
    const { data: gangsList, isLoading } = useQuery(api.gang.getGangList.queryOptions());
    const { data: currentUser } = useFetchCurrentUser();

    const handleViewGang = (gang) => {
        setSelectedGang(gang);
        setOpenModal(true);
    };

    return (
        <>
            <section className="mx-auto rounded-lg bg-gray-100 py-3 md:max-w-3xl dark:bg-gray-800">
                <div className="flex flex-col gap-2 p-1.5">
                    <div className="relative mb-6 text-center text-2xl text-custom-yellow">
                        <Link to={-1}>
                            {" "}
                            <Button className="!absolute left-0! h-10!">Back</Button>
                        </Link>

                        <p>Gang List</p>
                    </div>
                    <LoadingState isLoading={isLoading}>
                        <ViewGangModal
                            open={openModal}
                            setOpen={setOpenModal}
                            selectedGang={selectedGang}
                            setSelectedGang={setSelectedGang}
                            currentUser={currentUser}
                        />
                        {gangsList?.map((gang) => (
                            <GangBanner
                                key={gang.id}
                                gang={gang}
                                className="h-24"
                                onClick={() => handleViewGang(gang)}
                            />
                        ))}
                    </LoadingState>
                </div>
            </section>
        </>
    );
}
