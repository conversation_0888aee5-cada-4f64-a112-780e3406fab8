import Button from "@/components/Buttons/Button";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import { Modal } from "@/components/Modal/Modal";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useState } from "react";
import { Link } from "react-router-dom";
import useAssignGangRank from "../api/useAssignGangRank";
import useKickGangMember from "../api/useKickGangMember";

const gangRanks = {
    6: {
        name: "Leader",
        color: "text-red-500",
        powers: "Change Settings, Invite Members, Kick Members",
    },
    5: {
        name: "Lieutenant",
        color: "text-yellow-400",
        powers: "Change Settings, Invite Members, Kick Members",
    },
    4: { name: "Officer", color: "text-green-400", powers: "Invite Members, Kick Members" },
    3: { name: "Thug", color: "text-indigo-500", powers: "Invite Members" },
    2: { name: "Member", color: "text-blue-400" },
    1: { name: "Rookie", color: "text-gray-300" },
};

const rankChangeOptions = {
    6: [5, 4, 3, 2, 1],
    5: [4, 3, 2, 1],
    4: [3, 2, 1],
    3: [],
    2: [],
    1: [],
};

export default function GangMemberModal({ member, setMember, open, setOpen, currentUserRank }) {
    const { kickFromGang } = useKickGangMember();
    const { assignGangRank } = useAssignGangRank();
    const [currentRank, setCurrentRank] = useState(parseInt(member?.rank) || 1);

    let options = rankChangeOptions[currentUserRank];
    if (member?.rank >= currentUserRank) {
        options = [];
    }

    const handleClose = (change) => {
        setMember(null);
        setOpen(change);
    };

    const handleGangKick = () => {
        kickFromGang(member?.user?.id);
        setMember(null);
        setOpen(false);
    };

    const handleAssignRank = () => {
        assignGangRank({ studentId: member?.user?.id, rank: currentRank });
        setMember(null);
        setOpen(false);
    };
    if (!member) return null;
    return (
        <Modal
            showClose
            open={open}
            title="Gang Member"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-3xl!"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Su197a0.png`}
                    alt=""
                    className="mt-0.5 h-10 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="flex flex-col md:mx-8">
                <div className="mt-2 flex flex-col rounded-lg border border-gray-600 bg-gray-800 px-3 py-4 lg:flex-row">
                    <div className="grow space-y-6">
                        <div className="flex flex-col md:flex-row md:gap-14">
                            <div>
                                <div className="flex">
                                    <label
                                        htmlFor="gangmember"
                                        className="mb-0 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                                    >
                                        Gang Member
                                    </label>
                                    <Link className="ml-auto! md:hidden" to={`/profile/${member?.user?.id}`}>
                                        <Button className="text-sm! h-7! w-fit!">Profile</Button>
                                    </Link>
                                </div>

                                <div className="mt-0 flex gap-6">
                                    <div className="mt-1 flex gap-4 rounded-md shadow-xs">
                                        <DisplayAvatar src={member?.user} className="size-14 rounded-full" />
                                        <div className="my-auto text-stroke-sm">
                                            <p className="text-blue-500 text-lg">
                                                {member?.user?.username}{" "}
                                                <span className="text-gray-400">#{member?.user.id}</span>
                                            </p>
                                            <p className={cn(gangRanks[member?.rank]?.color, "text-base")}>
                                                Gang {gangRanks[member?.rank]?.name}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="mx-4">
                                <dt className="mt-6 block font-normal text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300">
                                    Last Active
                                </dt>
                                <dd className="overflow-auto text-gray-900 text-sm text-stroke-sm dark:text-indigo-500">
                                    {member?.user?.last_activity &&
                                        format(member?.user?.last_activity, "HH:mm dd/MM/yy")}
                                </dd>
                            </div>
                        </div>

                        {/* <div>
              <label
                htmlFor="about"
                className="mb-2 block text-xs font-bold uppercase tracking-wide text-gray-700 dark:text-gray-300 dark:font-normal"
              >
                Stats
              </label>
              <div className="focus:ring-light-blue-500 h-28 focus:border-light-blue-500 mt-1 block w-full rounded-md border-gray-300 shadow-xs sm:text-sm dark:border-gray-600 dark:text-gray-200 dark:bg-slate-900"></div>
            </div> */}
                        <div className="mt-1 flex w-full flex-col gap-4 md:flex-row">
                            <Link className="hidden md:block" to={`/profile/${member?.user?.id}`}>
                                <Button className="text-sm!">View Profile</Button>
                            </Link>
                            {options?.length > 0 && (
                                <div className="mx-2 flex w-full gap-2">
                                    <select
                                        id="currentRank"
                                        name={"currentRank"}
                                        value={parseInt(currentRank)}
                                        defaultValue={parseInt(member?.rank)}
                                        className="ml-2 w-32 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-lg shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                                        onChange={(e) => setCurrentRank(parseInt(e.target.value))}
                                    >
                                        {options.map((rank) => (
                                            <option key={rank} value={parseInt(rank)}>
                                                {gangRanks[rank]?.name}
                                            </option>
                                        ))}
                                    </select>
                                    <Button className="text-sm!" onClick={() => handleAssignRank()}>
                                        Change Rank
                                    </Button>
                                    <Button
                                        variant="destructive"
                                        className="text-sm! ml-auto!"
                                        onClick={() => handleGangKick()}
                                    >
                                        Kick Member
                                    </Button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </Modal>
    );
}
