import { Buff, Evolution, EvolutionStage } from "../features/pets/pets.types.js";
import { db } from "../lib/db.js";
import { Prisma } from "@prisma/client";

export const getPetById = async (petId: number) => {
    return await db.pet.findUnique({
        where: { id: petId },
    });
};

export async function findUserWithPets(userId: number) {
    return await db.user.findUnique({
        where: { id: userId },
        include: { user_pet: true },
    });
}

export const createUserPet = async (createPetData: Prisma.user_petCreateInput) => {
    return await db.user_pet.create({
        data: createPetData,
    });
};

export const getUserPets = async (userId: number) => {
    return await db.user_pet.findMany({
        where: { userId },
    });
};

export async function getUserPetsWithPetData(userId: number) {
    return await db.user_pet
        .findMany({
            where: { userId },
            include: { pet: true },
        })
        .then((userPets) =>
            userPets.map((userPet) => {
                // Get current evolution stage data from pet
                const pet = userPet.pet;
                const currentStage = userPet.evolution?.current || "egg";
                const evolutionStage = pet?.evolution_stages?.find(
                    (stage: EvolutionStage) => stage.stage === currentStage
                );

                return {
                    ...userPet,
                    image: evolutionStage?.image,
                    buffs: evolutionStage?.buffs,
                    species: pet?.species,
                    maxLevel: pet?.maxLevel,
                    name: userPet.name ?? pet?.name,
                    evolution: userPet.evolution,
                };
            })
        );
}

export async function getPets() {
    return await db.pet.findMany();
}

export async function updatePetEnergyAndHappiness(petId: number, energy: number, happiness: number) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            energy: Math.min(energy, 100),
            happiness: Math.min(happiness, 100),
        },
    });
}

export async function updatePetHappiness(petId: number, happiness: number) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            happiness: Math.min(happiness, 100),
        },
    });
}

export async function updatePetLevel(petId: number, level: number) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            level,
        },
    });
}

export async function updatePetXp(petId: number, xp: number, nextLevelXp: number) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            xp,
            nextLevelXp,
        },
    });
}

export async function updatePetLevelAndXp(petId: number, level: number, xp: number, nextLevelXp: number) {
    return await db.user_pet.update({
        where: { id: petId },
        data: {
            level,
            xp,
            nextLevelXp,
        },
    });
}

/**
 * Updates a pet's evolution progress
 * @param userPetId ID of the user pet to update
 * @param evolution Updated evolution data object
 * @returns Updated user pet object
 */
export async function updatePetEvolution(userPetId: number, evolution: Evolution) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: {
            evolution,
        },
    });
}

export async function updatePetName(userPetId: number, name: string) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: { name },
        include: { pet: true },
    });
}

export async function evolveUserPet(userPetId: number, evolutionData: Evolution) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: {
            level: 1, // Reset level after evolution
            happiness: 50, // Reset happiness after evolution
            xp: 0, // Reset XP after evolution
            nextLevelXp: 100, // Reset nextLevelXp after evolution
            evolution: evolutionData,
        },
        include: { pet: true },
    });
}

export async function findUserPetByIdAndOwner(userId: number, userPetId: number) {
    return await db.user_pet.findUnique({
        where: {
            id: userPetId,
            userId,
        },
        include: { pet: true },
    });
}

export async function setActivePet(userId: number, userPetId: number) {
    // First, deactivate all pets for this user
    await db.user_pet.updateMany({
        where: { userId, isActive: true },
        data: { isActive: false },
    });

    // Then, activate the specified pet
    return await db.user_pet.update({
        where: { id: userPetId, userId },
        data: { isActive: true },
        include: { pet: true },
    });
}

export async function updatePetBuffs(petId: number, buffs: Buff[]) {
    // Get the current pet data first
    const pet = await db.pet.findUnique({
        where: { id: petId },
    });

    if (!pet) {
        throw new Error("Pet not found");
    }

    // Get the current evolution stages
    const evolutionStages = (pet.evolution_stages as EvolutionStage[]) || [];

    // Update the buffs for each evolution stage
    const updatedEvolutionStages = evolutionStages.map((stage) => ({
        ...stage,
        buffs,
    }));

    // Update the pet with the modified evolution stages
    return await db.pet.update({
        where: { id: petId },
        data: {
            evolution_stages: updatedEvolutionStages,
        },
    });
}

export async function updatePetEvolutionProgress(userPetId: number, evolution: Evolution) {
    return await db.user_pet.update({
        where: { id: userPetId },
        data: { evolution },
    });
}
