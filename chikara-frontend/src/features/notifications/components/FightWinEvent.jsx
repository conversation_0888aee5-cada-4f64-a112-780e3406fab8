import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { Flame } from "lucide-react";

import { Link } from "react-router-dom";
import { formatCurrency } from "@/utils/currencyHelpers";

function FightWinEvent({ details, read }) {
    const { data: user } = useGetUserInfo(details.defeated);

    const postActionChosen = (action, mugAmount) => {
        switch (action) {
            case "mug":
                return (
                    <>
                        <span>and stole </span>
                        <span className="text-blue-600">{formatCurrency(mugAmount)}</span>
                    </>
                );
            case "cripple":
                return <span>and crippled them.</span>;
            case "leave":
                return <span>and left them after the battle</span>;
            default:
                return null;
        }
    };

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                <p>
                    You successfully attacked{" "}
                    <Link className="inline text-blue-600" to={`/profile/${details.defeated}`}>
                        {" "}
                        {user?.username}
                    </Link>{" "}
                    {postActionChosen(details?.action, details?.mugAmount)}
                </p>
            </td>
        </>
    );
}

export default FightWinEvent;
