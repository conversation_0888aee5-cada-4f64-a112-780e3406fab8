import { Alert, AlertDescription, AlertIcon, AlertTitle } from "@/components/ui/alert";
import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { calculateFee, formatCurrency } from "@/utils/currencyHelpers";
import { useMemo } from "react";

// Types
export type AlertType =
    | "Deposit"
    | "Withdraw"
    | "Transfer"
    | "DepositAmountTooLow"
    | "WithdrawAmountTooLow"
    | "TransferAmountTooLow"
    | "InvalidDepositAmount"
    | "InvalidWithdrawAmount";

interface BankAlertConfig {
    variant: "success" | "destructive" | "warning" | "info";
    title: string;
    description?: string;
}

interface InlineAlertProps {
    transactionType: AlertType;
    amount: number;
    transferID?: number;
    TRANSACTION_FEE?: number;
    MINIMUM_WITHDRAWAL?: number;
    MINIMUM_DEPOSIT?: number;
    MINIMUM_TRANSFER?: number;
    className?: string;
}

const InlineAlert = ({
    transactionType,
    amount,
    transferID,
    TRANSACTION_FEE = 0,
    MINIMUM_WITHDRAWAL = 0,
    MINIMUM_DEPOSIT = 0,
    MINIMUM_TRANSFER = 0,
    className,
}: InlineAlertProps) => {
    const parsedID = transferID ? Number.parseInt(String(transferID)) : undefined;
    const { data: user } = useGetUserInfo(parsedID!, {
        enabled: !!parsedID && transactionType === "Transfer",
    });

    const alertConfig: BankAlertConfig = useMemo(() => {
        const formattedAmount = formatCurrency(amount);
        const feeAmount = calculateFee(amount, TRANSACTION_FEE);
        const formattedFee = formatCurrency(feeAmount);

        switch (transactionType) {
            case "Deposit":
                return {
                    variant: "success",
                    title: `Successfully deposited ${formattedAmount}!`,
                    description:
                        TRANSACTION_FEE > 0
                            ? `A ${TRANSACTION_FEE}% deposit fee of ${formattedFee} was taken by the bank.`
                            : undefined,
                };
            case "Withdraw":
                return {
                    variant: "success",
                    title: `Successfully withdrew ${formattedAmount}!`,
                };
            case "Transfer":
                return {
                    variant: "success",
                    title: `Successfully transferred ${formattedAmount} to ${user?.username || "recipient"}!`,
                    description:
                        TRANSACTION_FEE > 0
                            ? `A ${TRANSACTION_FEE}% transfer fee of ${formattedFee} was taken by the bank.`
                            : undefined,
                };
            case "DepositAmountTooLow":
                return {
                    variant: "destructive",
                    title: `Minimum deposit amount is ${formatCurrency(MINIMUM_DEPOSIT)}`,
                };
            case "WithdrawAmountTooLow":
                return {
                    variant: "destructive",
                    title: `Minimum withdrawal amount is ${formatCurrency(MINIMUM_WITHDRAWAL)}`,
                };
            case "TransferAmountTooLow":
                return {
                    variant: "destructive",
                    title: `Minimum transfer amount is ${formatCurrency(MINIMUM_TRANSFER)}`,
                };
            case "InvalidDepositAmount":
                return {
                    variant: "destructive",
                    title: "Insufficient funds for deposit",
                    description: "You don't have enough cash to make this deposit.",
                };
            case "InvalidWithdrawAmount":
                return {
                    variant: "destructive",
                    title: "Insufficient bank balance",
                    description: "You don't have enough money in your bank account for this withdrawal.",
                };
            default:
                return {
                    variant: "info",
                    title: "Transaction processed",
                };
        }
    }, [
        transactionType,
        amount,
        user?.username,
        TRANSACTION_FEE,
        MINIMUM_DEPOSIT,
        MINIMUM_WITHDRAWAL,
        MINIMUM_TRANSFER,
    ]);

    return (
        <Alert variant={alertConfig.variant} className={className}>
            <AlertIcon variant={alertConfig.variant} />
            <AlertTitle>{alertConfig.title}</AlertTitle>
            {alertConfig.description && <AlertDescription>{alertConfig.description}</AlertDescription>}
        </Alert>
    );
};

export default InlineAlert;
