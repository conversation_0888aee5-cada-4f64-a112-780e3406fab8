import type { RouterClient } from "@orpc/server";
import { jobAdminRouter } from "./features/job/job.routes.js";
import { storyAdminRouter } from "./features/story/story.routes.js";
import { questAdminRouter } from "./features/quest/quest.admin.js";
import { shopAdminRouter } from "./features/shop/shop.routes.js";
import { craftingAdminRouter } from "./features/crafting/crafting.routes.js";
import { petsAdminRouter } from "./features/pets/pets.routes.js";
import { authAdminRouter } from "./features/auth/auth.routes.js";
import { creatureAdminRouter } from "./features/creature/creature.routes.js";
import { itemAdminRouter } from "./features/item/item.routes.js";
import { dropChanceAdminRouter } from "./features/dropchance/dropchance.routes.js";
import { actionlogRouter } from "./features/actionlog/actionlog.routes.js";
import { adminRouter } from "./features/admin/admin.routes.js";

export const adminAppRouter = {
    ...adminRouter,
    crafting: craftingAdminRouter,
    actionlog: actionlogRouter,
    quest: questAdminRouter,
    creature: creatureAdminRouter,
    auth: authAdminRouter,
    item: itemAdminRouter,
    dropChance: dropChanceAdminRouter,
    job: jobAdminRouter,
    pets: petsAdminRouter,
    shop: shopAdminRouter,
    story: storyAdminRouter,
};

export type AdminRouterClient = RouterClient<typeof adminAppRouter>;
