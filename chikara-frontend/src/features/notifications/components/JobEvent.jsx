import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { Sparkles } from "lucide-react";
import { formatCurrency } from "@/utils/currencyHelpers";

function JobEvent({ details, read }) {
    const { data: jobs } = useQuery(api.jobs.list.queryOptions());

    const findJobName = (jobId) => {
        if (jobs) {
            const job = jobs.filter((el) => el.id === jobId);
            return job[0]?.name;
        }
        return "";
    };

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={` px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                You received your daily salary of{" "}
                <span className="text-indigo-800 dark:text-indigo-400">{formatCurrency(details.amount)}</span> from
                working at {findJobName(details.job)}
            </td>
        </>
    );
}

export default JobEvent;
